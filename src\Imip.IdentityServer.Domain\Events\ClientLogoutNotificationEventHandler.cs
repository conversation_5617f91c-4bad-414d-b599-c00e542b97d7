using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EventBus.Distributed;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Events;

/// <summary>
/// Handles ClientLogoutNotificationEvent to notify specific client applications about logout
/// </summary>
public class ClientLogoutNotificationEventHandler : IDistributedEventHandler<ClientLogoutNotificationEvent>, ITransientDependency
{
    private readonly ILogger<ClientLogoutNotificationEventHandler> _logger;
    private readonly IHttpClientFactory _httpClientFactory;

    public ClientLogoutNotificationEventHandler(
        ILogger<ClientLogoutNotificationEventHandler> logger,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
    }

    public async Task HandleEventAsync(ClientLogoutNotificationEvent eventData)
    {
        _logger.LogInformation(
            "Processing logout notification for client {ClientId} for user {UserId} ({UserName})",
            eventData.ClientId, eventData.UserId, eventData.UserName);

        try
        {
            // Handle different notification types
            switch (eventData.NotificationType)
            {
                case LogoutNotificationType.FrontChannel:
                    await HandleFrontChannelLogoutAsync(eventData);
                    break;

                case LogoutNotificationType.BackChannel:
                    await HandleBackChannelLogoutAsync(eventData);
                    break;

                case LogoutNotificationType.Both:
                    await HandleFrontChannelLogoutAsync(eventData);
                    await HandleBackChannelLogoutAsync(eventData);
                    break;

                default:
                    _logger.LogWarning(
                        "Unknown logout notification type {NotificationType} for client {ClientId}",
                        eventData.NotificationType, eventData.ClientId);
                    break;
            }

            _logger.LogInformation(
                "Successfully processed logout notification for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing logout notification for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);

            // Don't rethrow to avoid blocking other notifications
            // Consider implementing retry logic or dead letter queue for failed notifications
        }
    }

    private Task HandleFrontChannelLogoutAsync(ClientLogoutNotificationEvent eventData)
    {
        if (string.IsNullOrEmpty(eventData.LogoutUrl))
        {
            _logger.LogDebug(
                "No front-channel logout URL configured for client {ClientId}",
                eventData.ClientId);
            return Task.CompletedTask;
        }

        try
        {
            _logger.LogDebug(
                "Initiating front-channel logout for client {ClientId} at URL {LogoutUrl}",
                eventData.ClientId, eventData.LogoutUrl);

            // For front-channel logout, we typically don't make direct HTTP calls
            // Instead, this would be handled by the browser through redirects
            // This handler could be used to log the event or trigger other actions

            _logger.LogInformation(
                "Front-channel logout initiated for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);

            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error handling front-channel logout for client {ClientId}",
                eventData.ClientId);
            throw;
        }
    }

    private async Task HandleBackChannelLogoutAsync(ClientLogoutNotificationEvent eventData)
    {
        if (string.IsNullOrEmpty(eventData.BackChannelLogoutUrl))
        {
            _logger.LogDebug(
                "No back-channel logout URL configured for client {ClientId}",
                eventData.ClientId);
            return;
        }

        try
        {
            _logger.LogDebug(
                "Initiating back-channel logout for client {ClientId} at URL {BackChannelLogoutUrl}",
                eventData.ClientId, eventData.BackChannelLogoutUrl);

            using var httpClient = _httpClientFactory.CreateClient();

            // Prepare the logout notification payload
            var logoutPayload = new
            {
                userId = eventData.UserId,
                userName = eventData.UserName,
                sessionId = eventData.SessionId,
                tenantId = eventData.TenantId,
                clientId = eventData.ClientId,
                sourceApplication = eventData.SourceApplication,
                logoutTimestamp = eventData.LogoutTimestamp,
                logoutToken = eventData.LogoutToken,
                metadata = eventData.Metadata
            };

            var jsonContent = JsonSerializer.Serialize(logoutPayload);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            // Set timeout for the HTTP request
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            // Add headers
            httpClient.DefaultRequestHeaders.Add("User-Agent", "IdentityServer-LogoutNotification/1.0");

            if (!string.IsNullOrEmpty(eventData.LogoutToken))
            {
                httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {eventData.LogoutToken}");
            }

            // Make the back-channel logout request
            var response = await httpClient.PostAsync(eventData.BackChannelLogoutUrl, content);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation(
                    "Successfully sent back-channel logout notification to client {ClientId} for user {UserId}",
                    eventData.ClientId, eventData.UserId);
            }
            else
            {
                _logger.LogWarning(
                    "Back-channel logout notification failed for client {ClientId} for user {UserId}. Status: {StatusCode}, Reason: {ReasonPhrase}",
                    eventData.ClientId, eventData.UserId, response.StatusCode, response.ReasonPhrase);
            }
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex,
                "HTTP error during back-channel logout for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);
            throw;
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _logger.LogError(ex,
                "Timeout during back-channel logout for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error during back-channel logout for client {ClientId} for user {UserId}",
                eventData.ClientId, eventData.UserId);
            throw;
        }
    }
}
