using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Testing;
using Volo.Abp.Uow;
using Xunit;
using Imip.IdentityServer.Domain.Services;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Tests.Services;

public class SessionManagementServiceTests : AbpIntegratedTest<IdentityServerDomainTestModule>
{
    private readonly SessionManagementService _service;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public SessionManagementServiceTests()
    {
        _sessionRepository = Substitute.For<IRepository<IdentitySession, Guid>>();
        _distributedEventBus = Substitute.For<IDistributedEventBus>();
        _unitOfWorkManager = GetRequiredService<IUnitOfWorkManager>();

        var logger = Substitute.For<ILogger<SessionManagementService>>();

        _service = new SessionManagementService(
            logger,
            _sessionRepository,
            _distributedEventBus,
            _unitOfWorkManager);
    }

    [Fact]
    public async Task GetActiveUserSessionsAsync_ShouldReturnOnlyActiveSessions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeSession = CreateTestSession(userId, "active-session", DateTime.UtcNow.AddMinutes(-30), DateTime.UtcNow.AddMinutes(-5));
        var expiredSession = CreateTestSession(userId, "expired-session", DateTime.UtcNow.AddDays(-2), DateTime.UtcNow.AddDays(-1));
        var loggedOutSession = CreateTestSession(userId, "logged-out-session", DateTime.UtcNow.AddYears(-1), DateTime.UtcNow.AddMinutes(-10));

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { activeSession, expiredSession, loggedOutSession });

        // Act
        var result = await _service.GetActiveUserSessionsAsync(userId);

        // Assert
        result.Count.ShouldBe(1);
        result.First().SessionId.ShouldBe("active-session");
    }

    [Fact]
    public async Task InvalidateSessionAsync_ShouldMarkSessionAsInvalidAndPublishEvent()
    {
        // Arrange
        var sessionId = "test-session";
        var userId = Guid.NewGuid();
        var session = CreateTestSession(userId, sessionId, DateTime.UtcNow.AddMinutes(-30), DateTime.UtcNow.AddMinutes(-5));

        _sessionRepository.FirstOrDefaultAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(session);

        // Act
        await _service.InvalidateSessionAsync(sessionId, SessionInvalidationReason.UserLogout, "TestApp");

        // Assert
        await _sessionRepository.Received(1).UpdateAsync(Arg.Is<IdentitySession>(s => 
            s.SessionId == sessionId && 
            s.SignedIn < DateTime.UtcNow.AddDays(-1)));

        await _distributedEventBus.Received(1).PublishAsync(Arg.Is<SessionInvalidationEvent>(e => 
            e.SessionId == sessionId && 
            e.Reason == SessionInvalidationReason.UserLogout));
    }

    [Fact]
    public async Task InvalidateAllUserSessionsAsync_ShouldInvalidateAllActiveSessionsAndPublishEvent()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var session1 = CreateTestSession(userId, "session-1", DateTime.UtcNow.AddMinutes(-30), DateTime.UtcNow.AddMinutes(-5));
        var session2 = CreateTestSession(userId, "session-2", DateTime.UtcNow.AddMinutes(-60), DateTime.UtcNow.AddMinutes(-10));

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { session1, session2 });

        // Act
        await _service.InvalidateAllUserSessionsAsync(userId, SessionInvalidationReason.PasswordChanged, "TestApp");

        // Assert
        await _sessionRepository.Received(2).UpdateAsync(Arg.Any<IdentitySession>());
        await _distributedEventBus.Received(1).PublishAsync(Arg.Is<SessionInvalidationEvent>(e => 
            e.UserId == userId && 
            e.SessionId == null && 
            e.ForceLogoutAllDevices == true &&
            e.Reason == SessionInvalidationReason.PasswordChanged));
    }

    [Fact]
    public async Task UpdateSessionLastAccessedAsync_ShouldUpdateLastAccessedTime()
    {
        // Arrange
        var sessionId = "test-session";
        var userId = Guid.NewGuid();
        var oldLastAccessed = DateTime.UtcNow.AddMinutes(-10);
        var session = CreateTestSession(userId, sessionId, DateTime.UtcNow.AddMinutes(-30), oldLastAccessed);

        _sessionRepository.FirstOrDefaultAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(session);

        // Act
        await _service.UpdateSessionLastAccessedAsync(sessionId);

        // Assert
        await _sessionRepository.Received(1).UpdateAsync(Arg.Is<IdentitySession>(s => 
            s.SessionId == sessionId && 
            s.LastAccessed > oldLastAccessed));
    }

    [Fact]
    public async Task UpdateSessionLastAccessedAsync_ShouldNotUpdateIfRecentlyUpdated()
    {
        // Arrange
        var sessionId = "test-session";
        var userId = Guid.NewGuid();
        var recentLastAccessed = DateTime.UtcNow.AddMinutes(-2); // Less than 5 minutes ago
        var session = CreateTestSession(userId, sessionId, DateTime.UtcNow.AddMinutes(-30), recentLastAccessed);

        _sessionRepository.FirstOrDefaultAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(session);

        // Act
        await _service.UpdateSessionLastAccessedAsync(sessionId);

        // Assert
        await _sessionRepository.DidNotReceive().UpdateAsync(Arg.Any<IdentitySession>());
    }

    [Fact]
    public void IsSessionActive_ShouldReturnTrueForActiveSessions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeSession = CreateTestSession(userId, "active-session", DateTime.UtcNow.AddMinutes(-30), DateTime.UtcNow.AddMinutes(-5));

        // Act
        var result = _service.IsSessionActive(activeSession);

        // Assert
        result.ShouldBeTrue();
    }

    [Fact]
    public void IsSessionActive_ShouldReturnFalseForExpiredSessions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var expiredSession = CreateTestSession(userId, "expired-session", DateTime.UtcNow.AddDays(-2), DateTime.UtcNow.AddDays(-1));

        // Act
        var result = _service.IsSessionActive(expiredSession);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public void IsSessionActive_ShouldReturnFalseForLoggedOutSessions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var loggedOutSession = CreateTestSession(userId, "logged-out-session", DateTime.UtcNow.AddYears(-1), DateTime.UtcNow.AddMinutes(-5));

        // Act
        var result = _service.IsSessionActive(loggedOutSession);

        // Assert
        result.ShouldBeFalse();
    }

    [Fact]
    public async Task GetUserSessionStatisticsAsync_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var activeSession1 = CreateTestSession(userId, "active-1", DateTime.UtcNow.AddMinutes(-30), DateTime.UtcNow.AddMinutes(-5), "Browser", "client-1");
        var activeSession2 = CreateTestSession(userId, "active-2", DateTime.UtcNow.AddMinutes(-60), DateTime.UtcNow.AddMinutes(-10), "Mobile", "client-2");
        var expiredSession = CreateTestSession(userId, "expired", DateTime.UtcNow.AddDays(-2), DateTime.UtcNow.AddDays(-1), "Browser", "client-1");

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { activeSession1, activeSession2, expiredSession });

        // Act
        var result = await _service.GetUserSessionStatisticsAsync(userId);

        // Assert
        result.UserId.ShouldBe(userId);
        result.TotalSessions.ShouldBe(3);
        result.ActiveSessions.ShouldBe(2);
        result.DeviceTypes["Browser"].ShouldBe(1);
        result.DeviceTypes["Mobile"].ShouldBe(1);
        result.ClientApplications["client-1"].ShouldBe(1);
        result.ClientApplications["client-2"].ShouldBe(1);
    }

    [Fact]
    public async Task CleanupExpiredSessionsAsync_ShouldDeleteExpiredSessions()
    {
        // Arrange
        var maxAge = TimeSpan.FromDays(7);
        var cutoffTime = DateTime.UtcNow - maxAge;

        var userId = Guid.NewGuid();
        var recentSession = CreateTestSession(userId, "recent", DateTime.UtcNow.AddDays(-1), DateTime.UtcNow.AddMinutes(-5));
        var expiredSession = CreateTestSession(userId, "expired", DateTime.UtcNow.AddDays(-10), cutoffTime.AddDays(-1));

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { expiredSession });

        // Act
        await _service.CleanupExpiredSessionsAsync(maxAge);

        // Assert
        await _sessionRepository.Received(1).DeleteAsync(expiredSession);
    }

    private static IdentitySession CreateTestSession(
        Guid userId, 
        string sessionId, 
        DateTime signedIn, 
        DateTime lastAccessed,
        string device = "Browser",
        string clientId = "test-client")
    {
        return new IdentitySession(
            Guid.NewGuid(),
            sessionId,
            device,
            "Test Device Info",
            userId,
            null,
            clientId,
            null,
            signedIn,
            lastAccessed);
    }
}
