'use client'
import { QueryNames } from '@/lib/hooks/QueryConstants'
import { useState } from 'react'

import { type IdentityRoleDto, type IdentityRoleUpdateDto } from '@/client'
import { type PaginationState, type SortingState } from '@tanstack/react-table'

import { useToast } from '@/lib/useToast'
import { Delete } from './Delete'

import { useQueryClient } from '@tanstack/react-query'
import { DataTable } from '@/components/data-table/DataTable'
import { getColumns } from './Columns'
import { Filter } from './Filter'
import { Add } from './Add'
import { Edit } from './Edit'
import { useRoles } from '@/lib/hooks/useRoles'
import { RolePermission } from './RolePermission'
import { TableSkeleton } from '@/components/ui/TableSkeleton'

export const List = () => {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const [searchStr, setSearchStr] = useState<string>('')
  const [userActionDialog, setUserActionDialog] = useState<{
    dataId: string
    dataEdit: IdentityRoleDto
    dialogType?: 'edit' | 'permission' | 'delete'
  } | null>()

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  })

  // Initialize sorting state
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false }
  ])

  // Convert SortingState to API sorting string
  const getSortingString = (sortState: SortingState): string => {
    if (!sortState.length) return 'name asc';

    const sort = sortState[0]; // Just use the first sort for now
    return `${sort?.id} ${sort?.desc ? 'desc' : 'asc'}`;
  }

  const { isLoading, data } = useRoles(
    pagination.pageIndex,
    pagination.pageSize,
    searchStr,
    getSortingString(sorting)
  )

  // Handler for user actions (edit, permission, delete)
  const handleUserAction = (dataId: string, dataEdit: IdentityRoleDto, dialogType: 'edit' | 'permission' | 'delete') => {
    setUserActionDialog({
      dataId,
      dataEdit,
      dialogType,
    })
  }

  // Get columns with the action handler
  const columns = getColumns(handleUserAction)

  const handleSearch = (value: string) => {
    setSearchStr(value)
    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on search
  }

  const handlePaginationChange = (newPagination: PaginationState) => {
    setPagination(newPagination)
  }

  // Handler for sorting change
  const handleSortingChange = (newSorting: SortingState) => {
    setSorting(newSorting)
    setPagination(prev => ({ ...prev, pageIndex: 0 })) // Reset to first page on sort change
  }

  // Handler for refreshing the data
  const handleRefresh = () => {
    // Invalidate the query to fetch fresh data
    void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })
    // Show toast notification after a short delay to match the animation
    setTimeout(() => {
      toast({
        title: "Data refreshed",
        description: "The claims list has been refreshed.",
        variant: "success",
      })
    }, 100)
  }

  if (isLoading) return (
    <TableSkeleton
      rowCount={pagination.pageSize}
      columnCount={4}
      hasTitle={true}
      hasSearch={true}
      hasFilters={true}
      hasPagination={true}
      hasActions={true}
    />
  )

  // Ensure we have valid data to render
  const items = data?.items ?? [];
  const totalCount = data?.totalCount ?? 0;

  return (
    <>
      <div className="space-y-4">
        <DataTable
          title="Roles"
          columns={columns}
          data={items}
          totalCount={totalCount}
          isLoading={isLoading}
          manualPagination={true}
          manualSorting={true}
          pageSize={pagination.pageSize}
          onPaginationChange={handlePaginationChange}
          onSortingChange={handleSortingChange}
          sortingState={sorting}
          onSearch={handleSearch}
          searchValue={searchStr}
          customFilterbar={Filter}
          hideDefaultFilterbar={true}
          onRefresh={handleRefresh}
          enableRowSelection={false}
          actionButton={{
            // label: "Create New User",
            onClick: () => { /* Required but not used */ },
            content: <Add />
          }}
        />
      </div>

      {userActionDialog && userActionDialog.dialogType === 'edit' && (
        <Edit
          dataId={userActionDialog.dataId}
          dataEdit={userActionDialog.dataEdit as IdentityRoleUpdateDto}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })
            setUserActionDialog(null)
          }}
        />
      )}
      {userActionDialog && userActionDialog.dialogType === 'delete' && (
        <Delete
          dataId={userActionDialog.dataId}
          onDismiss={() => {
            void queryClient.invalidateQueries({ queryKey: [QueryNames.GetRoles] })
            setUserActionDialog(null)
          }}
        />
      )}

      {userActionDialog && userActionDialog.dialogType === 'permission' && (
        <RolePermission
          roleDto={userActionDialog.dataEdit}
          onDismiss={() => setUserActionDialog(null)}
        />
      )}
    </>
  )
}
