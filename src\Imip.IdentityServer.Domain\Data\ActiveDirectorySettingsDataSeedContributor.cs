using System.Threading.Tasks;
using Imip.IdentityServer.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.SettingManagement;
using System;

namespace Imip.IdentityServer.Data;

public class ActiveDirectorySettingsDataSeedContributor : IDataSeedContributor, ITransientDependency
{
    private readonly ISettingManager _settingManager;
    private readonly IConfiguration _configuration;
    private readonly ILogger<ActiveDirectorySettingsDataSeedContributor> _logger;

    public ActiveDirectorySettingsDataSeedContributor(
        ISettingManager settingManager,
        IConfiguration configuration,
        ILogger<ActiveDirectorySettingsDataSeedContributor> logger)
    {
        _settingManager = settingManager;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task SeedAsync(DataSeedContext context)
    {
        _logger.LogInformation("Seeding Active Directory settings...");

        // Only seed if settings don't exist
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.Enabled,
            _configuration["ActiveDirectory:Enabled"] ?? "false");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.Domain,
            _configuration["ActiveDirectory:Domain"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.LdapServer,
            _configuration["ActiveDirectory:LdapServer"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.BaseDn,
            _configuration["ActiveDirectory:BaseDn"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.Username,
            _configuration["ActiveDirectory:Username"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.Password,
            _configuration["ActiveDirectory:Password"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.Port,
            _configuration["ActiveDirectory:Port"] ?? "389");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.UseSsl,
            _configuration["ActiveDirectory:UseSsl"] ?? "false");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.AutoLogin,
            _configuration["ActiveDirectory:AutoLogin"] ?? "false");

        // Handle encrypted TokenSecret setting with special care
        await SeedEncryptedSettingAsync(IdentityServerSettings.ActiveDirectory.TokenSecret,
            _configuration["ActiveDirectory:TokenSecret"] ?? "imip-identity-server-secure-token-key-default");

        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.DefaultUsername,
            _configuration["ActiveDirectory:DefaultUsername"] ?? "");
        await SeedSettingIfNotExistsAsync(IdentityServerSettings.ActiveDirectory.WindowsAuthEnabled,
            _configuration["ActiveDirectory:WindowsAuthEnabled"] ?? "false");

        _logger.LogInformation("Active Directory settings seeding completed.");
    }

    private async Task SeedSettingIfNotExistsAsync(string name, string value)
    {
        // Only set if the setting doesn't already exist in the database
        if (string.IsNullOrEmpty(await _settingManager.GetOrNullGlobalAsync(name)))
        {
            _logger.LogInformation("Seeding setting: {SettingName} with value from configuration", name);
            await _settingManager.SetGlobalAsync(name, value);
        }
        else
        {
            _logger.LogDebug("Setting {SettingName} already exists in database, skipping", name);
        }
    }

    private async Task SeedEncryptedSettingAsync(string name, string value)
    {
        try
        {
            // Try to get the existing setting to see if it can be decrypted
            var existingSetting = await _settingManager.GetOrNullGlobalAsync(name);

            if (string.IsNullOrEmpty(existingSetting))
            {
                // Setting doesn't exist, create it
                _logger.LogInformation("Seeding encrypted setting: {SettingName} with value from configuration", name);
                await _settingManager.SetGlobalAsync(name, value);
            }
            else
            {
                // Setting exists, verify it can be decrypted properly
                _logger.LogDebug("Encrypted setting {SettingName} already exists in database, verifying decryption", name);

                // If we can read it without errors, it's properly encrypted
                // The warning in logs indicates decryption failed, so we should reset it
                _logger.LogInformation("Re-seeding encrypted setting: {SettingName} to ensure proper encryption", name);
                await _settingManager.SetGlobalAsync(name, value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to handle encrypted setting {SettingName}, re-seeding with new value", name);
            await _settingManager.SetGlobalAsync(name, value);
        }
    }
}
