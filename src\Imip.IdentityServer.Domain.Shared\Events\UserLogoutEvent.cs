using System;
using Volo.Abp.EventBus;

namespace Imip.IdentityServer.Events;

/// <summary>
/// Distributed event for user logout propagation
/// </summary>
[EventName("Imip.IdentityServer.UserLogout")]
public class UserLogoutEvent
{
    /// <summary>
    /// User ID who is logging out
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Username of the user logging out
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Session ID being terminated
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// Tenant ID (for multi-tenant scenarios)
    /// </summary>
    public Guid? TenantId { get; set; }

    /// <summary>
    /// Source application/client that initiated the logout
    /// </summary>
    public string SourceApplication { get; set; } = string.Empty;

    /// <summary>
    /// Type of logout (FrontChannel, BackChannel, Manual)
    /// </summary>
    public LogoutType LogoutType { get; set; }

    /// <summary>
    /// Timestamp when the logout was initiated
    /// </summary>
    public DateTime LogoutTimestamp { get; set; }

    /// <summary>
    /// IP Address from where logout was initiated
    /// </summary>
    public string? IpAddress { get; set; }

    /// <summary>
    /// User agent information
    /// </summary>
    public string? UserAgent { get; set; }

    /// <summary>
    /// Additional metadata for the logout event
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// List of client applications that should be notified
    /// </summary>
    public string[]? TargetClients { get; set; }

    public UserLogoutEvent()
    {
        LogoutTimestamp = DateTime.UtcNow;
    }

    public UserLogoutEvent(
        Guid userId,
        string userName,
        string sourceApplication,
        LogoutType logoutType,
        string? sessionId = null,
        Guid? tenantId = null,
        string? ipAddress = null,
        string? userAgent = null,
        string[]? targetClients = null)
    {
        UserId = userId;
        UserName = userName;
        SourceApplication = sourceApplication;
        LogoutType = logoutType;
        SessionId = sessionId;
        TenantId = tenantId;
        IpAddress = ipAddress;
        UserAgent = userAgent;
        TargetClients = targetClients;
        LogoutTimestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Types of logout operations
/// </summary>
public enum LogoutType
{
    /// <summary>
    /// Manual logout initiated by user
    /// </summary>
    Manual = 0,

    /// <summary>
    /// Front-channel logout (browser-based)
    /// </summary>
    FrontChannel = 1,

    /// <summary>
    /// Back-channel logout (server-to-server)
    /// </summary>
    BackChannel = 2,

    /// <summary>
    /// Session timeout
    /// </summary>
    Timeout = 3,

    /// <summary>
    /// Administrative logout
    /// </summary>
    Administrative = 4
}
