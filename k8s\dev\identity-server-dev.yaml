# Development Environment Configuration for Identity Server
apiVersion: v1
kind: Namespace
metadata:
  name: imip-dev
---
# ConfigMap for application settings
apiVersion: v1
kind: ConfigMap
metadata:
  name: identity-server-config
  namespace: imip-dev
data:
  appsettings.json: |
    {
      "App": {
        "SelfUrl": "https://identity-dev.imip.local",
        "HealthCheckUrl": "/health",
        "ClientUrl": "https://client-dev.imip.local",
        "CorsOrigins": "https://client-dev.imip.local,http://localhost:3000",
        "AppName": "Imip.IdentityServer.Dev"
      },
      "RabbitMQ": {
        "Connections": {
          "Default": {
            "HostName": "**********",
            "UserName": "guest",
            "Password": "guest",
            "Port": 5672
          }
        },
        "EventBus": {
          "ClientName": "IdentityServer-Dev",
          "ExchangeName": "LogoutEvents"
        }
      },
      "ConnectionStrings": {
        "Default": "Server=sqlserver-dev.imip-dev.svc.cluster.local;Database=IdentityServer_Dev;User ID=sa;Password=DevPassword123;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"
      },
      "AuthServer": {
        "Authority": "https://identity-dev.imip.local",
        "RequireHttpsMetadata": true,
        "CertificatePassPhrase": "dev-cert-passphrase",
        "CertificatePath": "/app/certs/identity-server.pfx"
      },
      "StringEncryption": {
        "DefaultPassPhrase": "dev-encryption-key-16chars"
      },
      "Redis": {
        "IsEnabled": "true",
        "Configuration": "redis-dev.imip-dev.svc.cluster.local:6379"
      },
      "Serilog": {
        "MinimumLevel": {
          "Default": "Information",
          "Override": {
            "Microsoft": "Warning",
            "System": "Warning"
          }
        }
      }
    }
---
# Secret for sensitive data
apiVersion: v1
kind: Secret
metadata:
  name: identity-server-secrets
  namespace: imip-dev
type: Opaque
data:
  # Base64 encoded values
  db-password: RGV2UGFzc3dvcmQxMjM=  # DevPassword123
  cert-passphrase: ZGV2LWNlcnQtcGFzc3BocmFzZQ==  # dev-cert-passphrase
  encryption-key: ZGV2LWVuY3J5cHRpb24ta2V5LTE2Y2hhcnM=  # dev-encryption-key-16chars
---
# Deployment for Identity Server
apiVersion: apps/v1
kind: Deployment
metadata:
  name: identity-server
  namespace: imip-dev
  labels:
    app: identity-server
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: identity-server
  template:
    metadata:
      labels:
        app: identity-server
        environment: dev
    spec:
      containers:
      - name: identity-server
        image: imip/identity-server:dev-latest
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Development"
        - name: ASPNETCORE_URLS
          value: "https://+:443;http://+:80"
        - name: ASPNETCORE_Kestrel__Certificates__Default__Password
          valueFrom:
            secretKeyRef:
              name: identity-server-secrets
              key: cert-passphrase
        - name: ASPNETCORE_Kestrel__Certificates__Default__Path
          value: "/app/certs/identity-server.pfx"
        volumeMounts:
        - name: config-volume
          mountPath: /app/appsettings.json
          subPath: appsettings.json
        - name: cert-volume
          mountPath: /app/certs
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: identity-server-config
      - name: cert-volume
        secret:
          secretName: identity-server-certs
---
# Service for Identity Server
apiVersion: v1
kind: Service
metadata:
  name: identity-server-service
  namespace: imip-dev
  labels:
    app: identity-server
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: https
    port: 443
    targetPort: 443
  selector:
    app: identity-server
---
# Ingress for Identity Server
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: identity-server-ingress
  namespace: imip-dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
spec:
  tls:
  - hosts:
    - identity-dev.imip.local
    secretName: identity-server-tls
  rules:
  - host: identity-dev.imip.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: identity-server-service
            port:
              number: 443
