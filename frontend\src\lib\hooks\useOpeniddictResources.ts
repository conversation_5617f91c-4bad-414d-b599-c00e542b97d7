import { postApiOpeniddictResourcesList } from '@/client'
import { extractApiError, generateQueryParameters } from '@/lib/query-utils'
import { toast } from '@/lib/useToast'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

export const useOpeniddictResources = (
  pageIndex: number,
  pageSize: number,
  filter?: string  ,
  sorting?: string  
) => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictResources, pageIndex, pageSize, filter, sorting],
    queryFn: async () => {
      try {
        // Generate query parameters using the utility function
        const body = generateQueryParameters({
          pageIndex,
          pageSize,
          filter,
          sorting,
          filterField: 'clientId', // You can customize the field to filter on
        })

        const response = await postApiOpeniddictResourcesList({
          body,
        })

        return response.data?.data
      } catch (error) {
        // Use the error extraction utility
        const { title, description } = extractApiError(error, 'Error loading resources')

        // Show toast notification
        toast({
          title,
          description,
          variant: 'destructive',
        })

        // Return empty data to prevent UI crashes
        return { items: [], totalCount: 0 }
      }
    },
  })
}
