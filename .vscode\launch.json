{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "DotRush Debugger",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/src/Imip.IdentityServer.Web/bin/Debug/net9.0/Imip.IdentityServer.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.IdentityServer.Web",
            "preLaunchTask": "dotrush: Build",
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development",
                "ASPNETCORE_URLS": "https://localhost:44309"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "justMyCode": true // Enable Just My Code
        },
        {
            "name": "netcoredbg",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Imip.IdentityServer.Web/bin/Debug/net9.0/Imip.IdentityServer.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.IdentityServer.Web",
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "powershell",
                "pipeArgs": [
                    "-Command"
                ],
                "debuggerPath": "E:\\DotnetProject\\netcoredbg\\netcoredbg",
                "debuggerArgs": [
                    "--interpreter=vscode"
                ],
                "quoteArgs": true
            },
            "env": {
                "DOTNET_ENVIRONMENT": "Development"
            },
            "justMyCode": true // Enable Just My Code
        },
        {
            // Use IntelliSense to find out which attributes exist for C# debugging
            // Use hover for the description of the existing attributes
            // For further information visit https://github.com/dotnet/vscode-csharp/blob/main/debugger-launchjson.md.
            "name": ".NET Core Launch (web)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            // If you have changed target frameworks, make sure to update the program path.
            "program": "${workspaceFolder}/src/Imip.IdentityServer.Web/bin/Debug/net9.0/Imip.IdentityServer.Web.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.IdentityServer.Web",
            "stopAtEntry": false,
            // Enable launching a web browser when ASP.NET Core starts. For more information: https://aka.ms/VSCode-CS-LaunchJson-WebBrowser
            "serverReadyAction": {
                "action": "openExternally",
                "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            },
            "justMyCode": true // Enable Just My Code
        },
        {
            "name": "Launch DbMigrator",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Imip.IdentityServer.DbMigrator/bin/Debug/net9.0/Imip.IdentityServer.DbMigrator.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Imip.IdentityServer.DbMigrator",
            "stopAtEntry": false,
            "console": "integratedTerminal",
            "env": {
                "ASPNETCORE_ENVIRONMENT": "Development"
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ],
}