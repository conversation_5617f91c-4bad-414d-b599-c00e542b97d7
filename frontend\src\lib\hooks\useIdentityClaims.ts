import { postApiIdentityClaimsList } from '@/client'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'
import { generateExtendedQueryParameters } from '@/lib/query-utils-extended'
import { type FilterCondition } from '@/lib/interfaces/IFilterCondition'
import { toast } from '@/lib/useToast'
import { extractApiError } from '../query-utils'

export const useIdentityClaims = (
  pageIndex: number,
  pageSize: number,
  filterConditions: FilterCondition[] = [],
  sorting?: string  
) => {
  return useQuery({
    queryKey: [QueryNames.GetIdentityClaims, pageIndex, pageSize, JSON.stringify(filterConditions), sorting],
    queryFn: async () => {
      try {
        // Generate query parameters using the extended utility function
        const body = generateExtendedQueryParameters({
          pageIndex,
          pageSize,
          sorting,
          filterConditions,
        })


        const response = await postApiIdentityClaimsList({
          body
        })

        return response.data?.data
      } catch (error) {
       // Use the error extraction utility
       const { title, description } = extractApiError(error, 'Error loading claims')

       // Show toast notification
       toast({
         title,
         description,
         variant: 'destructive',
       })
        // Return empty data to prevent UI crashes
        return { items: [], totalCount: 0 }
      }
    },
  })
}
