#!/usr/bin/env pwsh
# PowerShell script to fix encrypted settings issues in production

param(
    [string]$Namespace = "imip-identity-prod",
    [string]$DatabaseName = "identityprovider_prd",
    [switch]$DryRun = $false,
    [switch]$Help = $false
)

if ($Help) {
    Write-Host @"
Fix Encrypted Settings Script

This script helps resolve CryptographicException issues with encrypted settings
in the ABP Framework Identity Server application.

Usage:
    ./fix-encrypted-settings.ps1 [-Namespace <namespace>] [-DatabaseName <dbname>] [-DryRun] [-Help]

Parameters:
    -Namespace      Kubernetes namespace (default: imip-identity-prod)
    -DatabaseName   Database name (default: identityprovider_prd)
    -DryRun         Show what would be done without making changes
    -Help           Show this help message

Examples:
    ./fix-encrypted-settings.ps1                           # Fix in production
    ./fix-encrypted-settings.ps1 -DryRun                   # Preview changes
    ./fix-encrypted-settings.ps1 -Namespace imip-identity-dev -DatabaseName identityprovider_dev
"@
    exit 0
}

Write-Host "=== ABP Framework Encrypted Settings Fix ===" -ForegroundColor Cyan
Write-Host "Namespace: $Namespace" -ForegroundColor Yellow
Write-Host "Database: $DatabaseName" -ForegroundColor Yellow
Write-Host "Dry Run: $DryRun" -ForegroundColor Yellow
Write-Host ""

# Function to execute kubectl commands
function Invoke-Kubectl {
    param([string]$Command)
    
    Write-Host "Executing: kubectl $Command" -ForegroundColor Gray
    if (-not $DryRun) {
        $result = Invoke-Expression "kubectl $Command"
        return $result
    } else {
        Write-Host "[DRY RUN] Would execute: kubectl $Command" -ForegroundColor Yellow
        return $null
    }
}

# Function to check if namespace exists
function Test-Namespace {
    param([string]$Namespace)
    
    try {
        $result = kubectl get namespace $Namespace 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

# Function to get database connection string
function Get-DatabaseConnection {
    param([string]$Namespace)
    
    try {
        $connectionString = kubectl get secret imip-identity-secrets -n $Namespace -o jsonpath='{.data.ConnectionStrings__Default}' 2>$null
        if ($connectionString) {
            return [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($connectionString))
        }
    } catch {
        Write-Warning "Could not retrieve database connection string from secret"
    }
    return $null
}

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Green

# Check if kubectl is available
try {
    kubectl version --client --short 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        throw "kubectl not found"
    }
    Write-Host "✓ kubectl is available" -ForegroundColor Green
} catch {
    Write-Error "kubectl is not available. Please install kubectl and ensure it's in your PATH."
    exit 1
}

# Check if namespace exists
if (-not (Test-Namespace $Namespace)) {
    Write-Error "Namespace '$Namespace' does not exist."
    exit 1
}
Write-Host "✓ Namespace '$Namespace' exists" -ForegroundColor Green

# Check if secrets exist
$secretExists = $false
try {
    kubectl get secret imip-identity-secrets -n $Namespace 2>$null | Out-Null
    $secretExists = $LASTEXITCODE -eq 0
} catch {
    $secretExists = $false
}

if (-not $secretExists) {
    Write-Error "Secret 'imip-identity-secrets' not found in namespace '$Namespace'"
    exit 1
}
Write-Host "✓ Required secrets exist" -ForegroundColor Green

# Get current encryption passphrase
Write-Host "`nChecking encryption configuration..." -ForegroundColor Green
$encryptionPassphrase = kubectl get secret imip-identity-secrets -n $Namespace -o jsonpath='{.data.StringEncryption__DefaultPassPhrase}' 2>$null
if ($encryptionPassphrase) {
    $encryptionPassphrase = [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($encryptionPassphrase))
    Write-Host "✓ Encryption passphrase found in secrets" -ForegroundColor Green
} else {
    Write-Warning "Could not retrieve encryption passphrase from secrets"
}

# Solution 1: Clean up corrupted settings using SQL
Write-Host "`nSolution 1: Database cleanup" -ForegroundColor Cyan
Write-Host "This will remove corrupted encrypted settings from the database." -ForegroundColor Yellow
Write-Host "The settings will be re-created during the next migration." -ForegroundColor Yellow

$sqlScript = @"
USE [$DatabaseName];
DELETE FROM AbpSettings WHERE Name = 'IdentityServer.ActiveDirectory.TokenSecret';
PRINT 'Removed corrupted TokenSecret setting';
"@

Write-Host "SQL Script to execute:" -ForegroundColor Gray
Write-Host $sqlScript -ForegroundColor DarkGray

if (-not $DryRun) {
    $response = Read-Host "`nDo you want to execute the SQL cleanup? (y/N)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Write-Host "Please execute the SQL script manually on your database server." -ForegroundColor Yellow
        Write-Host "The script is also available in: scripts/fix-encrypted-settings.sql" -ForegroundColor Yellow
    }
}

# Solution 2: Restart migration job
Write-Host "`nSolution 2: Re-run database migration" -ForegroundColor Cyan
Write-Host "This will trigger a new migration job to re-seed the settings." -ForegroundColor Yellow

if (-not $DryRun) {
    $response = Read-Host "`nDo you want to trigger a new migration job? (y/N)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        # Delete existing migration jobs
        Write-Host "Cleaning up existing migration jobs..." -ForegroundColor Yellow
        Invoke-Kubectl "delete jobs -n $Namespace -l app=imip-identity-db-migrator --ignore-not-found=true"
        
        # Apply the migration job
        Write-Host "Starting new migration job..." -ForegroundColor Yellow
        Invoke-Kubectl "apply -f k8s/prod/db-migrator-job.yaml"
        
        # Monitor the job
        Write-Host "Monitoring migration job..." -ForegroundColor Yellow
        Invoke-Kubectl "get jobs -n $Namespace -l app=imip-identity-db-migrator -w"
    }
}

# Solution 3: Update secrets with new values
Write-Host "`nSolution 3: Update TokenSecret in secrets" -ForegroundColor Cyan
Write-Host "This will update the TokenSecret value in Kubernetes secrets." -ForegroundColor Yellow

$newTokenSecret = "imip-identity-server-secure-token-key-prod-$(Get-Date -Format 'yyyyMMdd')"
Write-Host "New TokenSecret value: $newTokenSecret" -ForegroundColor Gray

if (-not $DryRun) {
    $response = Read-Host "`nDo you want to update the TokenSecret in secrets? (y/N)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        # Update the secret
        $base64TokenSecret = [System.Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes($newTokenSecret))
        Invoke-Kubectl "patch secret imip-identity-secrets -n $Namespace -p '{`"data`":{`"ActiveDirectory__TokenSecret`":`"$base64TokenSecret`"}}''"
        
        Write-Host "✓ TokenSecret updated in secrets" -ForegroundColor Green
        Write-Host "You may need to restart the application pods to pick up the new value." -ForegroundColor Yellow
    }
}

Write-Host "`n=== Summary ===" -ForegroundColor Cyan
Write-Host "The CryptographicException occurs when:" -ForegroundColor Yellow
Write-Host "1. Settings were encrypted with a different passphrase" -ForegroundColor White
Write-Host "2. The encryption key has changed between environments" -ForegroundColor White
Write-Host "3. The encrypted data in the database is corrupted" -ForegroundColor White
Write-Host ""
Write-Host "Recommended actions:" -ForegroundColor Yellow
Write-Host "1. Execute the SQL cleanup script to remove corrupted settings" -ForegroundColor White
Write-Host "2. Run a new database migration to re-seed the settings" -ForegroundColor White
Write-Host "3. Verify the encryption passphrase is consistent across environments" -ForegroundColor White
Write-Host ""
Write-Host "The warning is non-critical and the application will continue to work," -ForegroundColor Green
Write-Host "but fixing it will eliminate the warning messages." -ForegroundColor Green
