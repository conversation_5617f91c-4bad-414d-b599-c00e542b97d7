#!/bin/bash

# Script to fix production migration issues
# This script should be run on the Kubernetes master node with kubectl access

set -e

echo "=== Production Migration Fix Script ==="
echo "This script will diagnose and fix common issues with the production migration job"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

print_status "Checking Kubernetes cluster connectivity..."
if ! kubectl cluster-info &>/dev/null; then
    print_error "Cannot connect to Kubernetes cluster"
    exit 1
fi

print_status "Checking production namespace..."
kubectl create namespace imip-identity-prod --dry-run=client -o yaml | kubectl apply -f -

print_status "Checking node availability..."
if ! kubectl get node imprdapp27 &>/dev/null; then
    print_error "Production node 'imprdapp27' not found"
    kubectl get nodes
    exit 1
fi

print_status "Checking if data protection directory exists on production node..."
# Create a debug pod to check and create the directory
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: debug-directory-check
  namespace: imip-identity-prod
spec:
  nodeSelector:
    kubernetes.io/hostname: imprdapp27
  containers:
  - name: debug
    image: busybox:1.35
    command: ['sh', '-c', 'sleep 3600']
    volumeMounts:
    - name: host-root
      mountPath: /host
  volumes:
  - name: host-root
    hostPath:
      path: /
      type: Directory
  restartPolicy: Never
EOF

print_status "Waiting for debug pod to start..."
kubectl wait --for=condition=ready pod/debug-directory-check -n imip-identity-prod --timeout=60s

print_status "Creating data protection directory on production node..."
kubectl exec -n imip-identity-prod debug-directory-check -- sh -c "
    mkdir -p /host/mnt/data/identity-data-protection
    chmod 755 /host/mnt/data/identity-data-protection
    ls -la /host/mnt/data/
    echo 'Directory created successfully'
"

print_status "Cleaning up debug pod..."
kubectl delete pod debug-directory-check -n imip-identity-prod

print_status "Checking PersistentVolume status..."
kubectl get pv imip-identity-data-protection-pv-prod || print_warning "PV not found"

print_status "Checking PersistentVolumeClaim status..."
kubectl get pvc -n imip-identity-prod imip-identity-data-protection || print_warning "PVC not found"

print_status "Checking secrets..."
kubectl get secret -n imip-identity-prod imip-identity-secrets || print_warning "Secrets not found"
kubectl get secret -n imip-identity-prod imip-identity-certificate || print_warning "Certificate secret not found"
kubectl get secret -n imip-identity-prod gitlab-registry-credentials || print_warning "Registry credentials not found"

print_status "Checking configmap..."
kubectl get configmap -n imip-identity-prod imip-identity-config || print_warning "ConfigMap not found"

print_status "Checking for existing migration jobs..."
EXISTING_JOBS=$(kubectl get jobs -n imip-identity-prod -o name | grep "imip-identity-db-migrator" || true)
if [ -n "$EXISTING_JOBS" ]; then
    print_warning "Found existing migration jobs:"
    echo "$EXISTING_JOBS"
    print_status "Cleaning up old jobs..."
    echo "$EXISTING_JOBS" | xargs kubectl delete -n imip-identity-prod
fi

print_status "Production migration environment check completed!"
print_status "You can now retry the migration job."

echo ""
echo "=== Next Steps ==="
echo "1. Ensure all secrets and configmaps are properly created"
echo "2. Verify the Docker image exists in the registry"
echo "3. Run the migration job again"
echo ""
echo "To check the status of a migration job:"
echo "kubectl get pods -n imip-identity-prod -l app=imip-identity-db-migrator"
echo ""
echo "To get logs from a migration pod:"
echo "kubectl logs -n imip-identity-prod -l app=imip-identity-db-migrator"
