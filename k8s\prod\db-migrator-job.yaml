﻿apiVersion: batch/v1
kind: Job
metadata:
  name: imip-identity-db-migrator-${CI_COMMIT_SHA}
  namespace: imip-identity-prod
spec:
  # Reduced backoffLimit for faster failure detection
  backoffLimit: 2
  # Add activeDeadlineSeconds to prevent long-running jobs
  activeDeadlineSeconds: 600
  template:
    metadata:
      labels:
        app: imip-identity-db-migrator
        commit: "${CI_COMMIT_SHA}"
    spec:
      # Force scheduling on the production node where PV is available
      nodeSelector:
        kubernetes.io/hostname: imprdapp27
      volumes:
        - name: certificate-volume
          secret:
            secretName: imip-identity-certificate
        - name: data-protection-keys
          persistentVolumeClaim:
            claimName: imip-identity-data-protection
      # Explicitly specify the image pull secrets
      imagePullSecrets:
        - name: gitlab-registry-credentials
      # Add init container to ensure directories exist and debug volume issues
      initContainers:
        - name: init-volumes
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              echo "Checking and creating required directories..."
              mkdir -p /app/data-protection-keys
              chmod 755 /app/data-protection-keys
              echo "Directory /app/data-protection-keys created and permissions set"
              ls -la /app/
              echo "Checking certificate volume..."
              ls -la /app/certs/ || echo "Certificate volume not mounted or empty"
              echo "Init container completed successfully"
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
            - name: data-protection-keys
              mountPath: /app/data-protection-keys
      containers:
        - name: imip-identity-db-migrator
          image: ${CI_REGISTRY_IMAGE}/db-migrator:${CI_COMMIT_SHA}
          # Add resource requests and limits for better scheduling
          resources:
            requests:
              cpu: "200m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          volumeMounts:
            - name: certificate-volume
              mountPath: /app/certs
              readOnly: true
            - name: data-protection-keys
              mountPath: /app/data-protection-keys
          envFrom:
            - configMapRef:
                name: imip-identity-config
            - secretRef:
                name: imip-identity-secrets
      restartPolicy: Never
      hostAliases:
        - hostnames:
            - api-identity-dev.imip.co.id
            - api-identity.imip.co.id
            - identity.imip.co.id
            - identity-dev.imip.co.id
          ip: **********
        - hostnames:
            - imaddc01.corp.imip.co.id
            - corp.imip.co.id
          ip: **************
