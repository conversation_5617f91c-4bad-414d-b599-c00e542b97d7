import {
  type IdentityUserUpdateDto,
  type PermissionGroupDto,
  type UpdatePermissionsDto,
  putApiPermissionManagementPermissions,
} from '@/client'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { useToast } from "@/lib/useToast"
import { usePermissions } from '@/lib/hooks/usePermissions'
import { useUserRoles } from '@/lib/hooks/useUserRoles'
import { PermissionProvider, USER_ROLE } from '@/lib/utils'
import { useQueryClient } from '@tanstack/react-query'
import { type FormEvent, useCallback, useEffect, useMemo, useState } from 'react'
import { type Management, Permission } from '@/components/app/permission/PermissionToggle'
import { TogglePermission } from '@/components/app/permission/TogglePermission'

type UserPermissionProps = {
  userDto: IdentityUserUpdateDto
  userId: string
  onDismiss: () => void
}

export const UserPermission = ({ userDto, userId, onDismiss }: UserPermissionProps) => {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const userRoles = useUserRoles({ userId })

  // flag determine to enable/disable all the permissions to a user.
  const [hasAllGranted, setHasAllGranted] = useState(false)
  const { data } = usePermissions(PermissionProvider.U, userId)
  const queryClient = useQueryClient()

  const [permissionGroups, setPermissionGroups] = useState<PermissionGroupDto[]>([])

  useEffect(() => {
    setOpen(true)
    return () => {
      void queryClient.invalidateQueries({ queryKey: [PermissionProvider.U] })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Update the local state with the remote data
  useEffect(() => {
    if (data?.groups && Array.isArray(data.groups)) {
      setPermissionGroups([...data.groups])
    }
  }, [data])

  // check if user have all the permissions are granted already.
  // Only run this when data changes, not when permissionGroups changes internally
  useEffect(() => {
    if (data?.groups && data.groups.length > 0) {
      const hasAllPermissionGranted = data.groups
        .map((g) => g.permissions?.every((p) => p.isGranted))
        .every((e) => e)
      setHasAllGranted(hasAllPermissionGranted)
    }
  }, [data])

  // Apply hasAllGranted to all permissions - only when hasAllGranted changes
  useEffect(() => {
    if (permissionGroups.length > 0) {
      const updatedGroups = permissionGroups.map(group => {
        // Create a new group object to avoid reference issues
        return {
          ...group,
          permissions: group.permissions?.map(permission => ({
            ...permission,
            isGranted: hasAllGranted
          }))
        };
      });

      // Break the circular dependency by using a ref to track updates
      setPermissionGroups(updatedGroups);
    }
  }, [hasAllGranted]); // Remove permissionGroups from dependency array

  const onSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault()
      const payload = permissionGroups
        ?.map((p) =>
          p.permissions!.map((grant) => ({
            name: grant.name,
            isGranted: grant.isGranted,
          }))
        )
        .flat()
      const requestPayload: UpdatePermissionsDto = {
        permissions: payload,
      }
      try {
        await putApiPermissionManagementPermissions({
          query: {
            providerName: PermissionProvider.U,
            providerKey: userId
          },
          body: requestPayload
        })
        toast({
          title: 'Success',
          description: 'Permission Updated Successfully',
          variant: 'default',
        })
        void queryClient.invalidateQueries({
          queryKey: [PermissionProvider.U],
        })
        onCloseEvent()
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast({
            title: 'Failed',
            description: "Permission update wasn't successful.",
            variant: 'destructive',
          })
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [permissionGroups]
  )

  const onCloseEvent = useCallback(() => {
    setOpen(false)
    onDismiss()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const hasAdmin = useMemo(() => {
    if (userRoles?.data?.items) {
      return userRoles.data.items.filter((role) => role.name?.includes(USER_ROLE.ADMIN)).length > 0
    }
    return false
  }, [userRoles])

  const formatDisplayName = (str: string | undefined): Management => {
    if (!str) return 'identity' as Management;
    return (str.split(' ')[0] ?? '').toLowerCase() as Management
  }

  // Group permissions into pairs for the grid layout
  const groupedPermissions = useMemo(() => {
    const result = [];
    for (let i = 0; i < permissionGroups.length; i += 2) {
      result.push(permissionGroups.slice(i, i + 2));
    }
    return result;
  }, [permissionGroups]);

  return (
    <Dialog open={open} onOpenChange={onCloseEvent}>
      <DialogContent size={'4xl'} className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Permissions - {userDto.userName}</DialogTitle>
        </DialogHeader>
        <form onSubmit={onSubmit} className="flex-1 overflow-y-auto">
          <div className="p-1">
            <Permission
              name="Grant All Permissions"
              isGranted={hasAllGranted}
              id="all_granted"
              disabled={!hasAdmin}
              onUpdate={() => {
                setHasAllGranted((prev) => !prev)
              }}
              className="ml-2 mb-4"
            />

            <div className="space-y-6">
              {groupedPermissions.map((row, rowIndex) => (
                <div key={rowIndex} className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {row.map((group, colIndex) => {
                    // Ensure group is properly typed
                    const displayName = group.displayName ?? '';
                    const permissions = group.permissions ?? [];

                    return (
                      <div key={`${rowIndex}-${colIndex}`} className="border rounded-lg p-4">
                        <h3 className="text-lg font-medium mb-2">{displayName}</h3>
                        <div className="border-t pt-3">
                          <TogglePermission
                            permissions={permissions}
                            type={formatDisplayName(displayName)}
                            disabled={hasAllGranted && hasAdmin}
                            hideSelectAll={hasAllGranted}
                            hideSave
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          </div>
        </form>
        <DialogFooter className="mt-4 border-t pt-4 bg-white dark:bg-gray-950">
          <Button
            onClick={(e) => {
              e.preventDefault()
              onCloseEvent()
            }}
            variant="ghost"
          >
            Cancel
          </Button>
          <Button onClick={onSubmit}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
