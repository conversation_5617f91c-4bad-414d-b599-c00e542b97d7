using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using NSubstitute;
using Shouldly;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.OpenIddict.Applications;
using Volo.Abp.Testing;
using Volo.Abp.Uow;
using Xunit;
using Imip.IdentityServer.Domain.Events;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Tests.Events;

public class UserLogoutEventHandlerTests : AbpIntegratedTest<IdentityServerDomainTestModule>
{
    private readonly UserLogoutEventHandler _handler;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;
    private readonly IRepository<OpenIddictApplication, Guid> _applicationRepository;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public UserLogoutEventHandlerTests()
    {
        _sessionRepository = Substitute.For<IRepository<IdentitySession, Guid>>();
        _applicationRepository = Substitute.For<IRepository<OpenIddictApplication, Guid>>();
        _distributedEventBus = Substitute.For<IDistributedEventBus>();
        _unitOfWorkManager = GetRequiredService<IUnitOfWorkManager>();

        var logger = Substitute.For<ILogger<UserLogoutEventHandler>>();

        _handler = new UserLogoutEventHandler(
            logger,
            _distributedEventBus,
            _sessionRepository,
            _applicationRepository,
            _unitOfWorkManager);
    }

    [Fact]
    public async Task HandleEventAsync_ShouldInvalidateUserSessions()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var sessionId = "test-session-123";
        var userName = "testuser";

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            "TestApp",
            LogoutType.Manual,
            sessionId);

        var existingSession = new IdentitySession(
            Guid.NewGuid(),
            sessionId,
            "Browser",
            "Chrome",
            userId,
            null,
            "test-client",
            null,
            DateTime.UtcNow.AddHours(-1),
            DateTime.UtcNow.AddMinutes(-5));

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { existingSession });

        _applicationRepository.GetListAsync()
            .Returns(new List<OpenIddictApplication>());

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        await _sessionRepository.Received(1).UpdateAsync(Arg.Any<IdentitySession>());
        await _distributedEventBus.Received(1).PublishAsync(Arg.Any<SessionInvalidationEvent>());
    }

    [Fact]
    public async Task HandleEventAsync_ShouldNotifyClientApplications()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userName = "testuser";

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            "SourceApp",
            LogoutType.Manual);

        var clientApp = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = "test-client",
            DisplayName = "Test Client",
            PostLogoutRedirectUris = "https://client.example.com/logout"
        };

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession>());

        _applicationRepository.GetListAsync()
            .Returns(new List<OpenIddictApplication> { clientApp });

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        await _distributedEventBus.Received(1).PublishAsync(Arg.Any<ClientLogoutNotificationEvent>());
        await _distributedEventBus.Received(1).PublishAsync(Arg.Any<SessionInvalidationEvent>());
    }

    [Fact]
    public async Task HandleEventAsync_ShouldSkipSourceApplication()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userName = "testuser";
        var sourceApp = "SourceApp";

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            sourceApp,
            LogoutType.Manual);

        var sourceClientApp = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = sourceApp,
            DisplayName = "Source App"
        };

        var otherClientApp = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = "other-client",
            DisplayName = "Other Client",
            PostLogoutRedirectUris = "https://other.example.com/logout"
        };

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession>());

        _applicationRepository.GetListAsync()
            .Returns(new List<OpenIddictApplication> { sourceClientApp, otherClientApp });

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        // Should only publish one client notification (for the other client, not the source)
        await _distributedEventBus.Received(1).PublishAsync(Arg.Is<ClientLogoutNotificationEvent>(e => e.ClientId == "other-client"));
        await _distributedEventBus.DidNotReceive().PublishAsync(Arg.Is<ClientLogoutNotificationEvent>(e => e.ClientId == sourceApp));
    }

    [Fact]
    public async Task HandleEventAsync_WithTargetClients_ShouldOnlyNotifySpecifiedClients()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userName = "testuser";
        var targetClients = new[] { "target-client-1", "target-client-2" };

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            "SourceApp",
            LogoutType.Manual,
            targetClients: targetClients);

        var targetClient1 = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = "target-client-1",
            DisplayName = "Target Client 1"
        };

        var targetClient2 = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = "target-client-2",
            DisplayName = "Target Client 2"
        };

        var otherClient = new OpenIddictApplication(Guid.NewGuid())
        {
            ClientId = "other-client",
            DisplayName = "Other Client"
        };

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession>());

        _applicationRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<OpenIddictApplication, bool>>>())
            .Returns(new List<OpenIddictApplication> { targetClient1, targetClient2 });

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        await _distributedEventBus.Received(2).PublishAsync(Arg.Any<ClientLogoutNotificationEvent>());
        await _distributedEventBus.Received(1).PublishAsync(Arg.Is<ClientLogoutNotificationEvent>(e => e.ClientId == "target-client-1"));
        await _distributedEventBus.Received(1).PublishAsync(Arg.Is<ClientLogoutNotificationEvent>(e => e.ClientId == "target-client-2"));
    }

    [Fact]
    public async Task HandleEventAsync_WithNoSessions_ShouldStillNotifyClients()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userName = "testuser";

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            "SourceApp",
            LogoutType.Manual);

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession>());

        _applicationRepository.GetListAsync()
            .Returns(new List<OpenIddictApplication>());

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        await _distributedEventBus.Received(1).PublishAsync(Arg.Any<SessionInvalidationEvent>());
    }

    [Fact]
    public async Task HandleEventAsync_WithMultipleSessions_ShouldInvalidateAll()
    {
        // Arrange
        var userId = Guid.NewGuid();
        var userName = "testuser";

        var logoutEvent = new UserLogoutEvent(
            userId,
            userName,
            "SourceApp",
            LogoutType.Administrative);

        var session1 = new IdentitySession(
            Guid.NewGuid(),
            "session-1",
            "Browser",
            "Chrome",
            userId,
            null,
            "client-1",
            null,
            DateTime.UtcNow.AddHours(-1),
            DateTime.UtcNow.AddMinutes(-5));

        var session2 = new IdentitySession(
            Guid.NewGuid(),
            "session-2",
            "Mobile",
            "iOS App",
            userId,
            null,
            "client-2",
            null,
            DateTime.UtcNow.AddHours(-2),
            DateTime.UtcNow.AddMinutes(-10));

        _sessionRepository.GetListAsync(Arg.Any<System.Linq.Expressions.Expression<System.Func<IdentitySession, bool>>>())
            .Returns(new List<IdentitySession> { session1, session2 });

        _applicationRepository.GetListAsync()
            .Returns(new List<OpenIddictApplication>());

        // Act
        await _handler.HandleEventAsync(logoutEvent);

        // Assert
        await _sessionRepository.Received(2).UpdateAsync(Arg.Any<IdentitySession>());
    }
}
