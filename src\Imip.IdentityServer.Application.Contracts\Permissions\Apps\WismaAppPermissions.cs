﻿using Imip.IdentityServer.Permissions.Base;

namespace Imip.IdentityServer.Permissions.Apps;

public class WismaAppPermissions
{
    public const string GroupName = "WismaApp";

    public static class PolicyRoomType
    {
        public const string Default = GroupName + ".RoomType";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyRoomStatus
    {
        public const string Default = GroupName + ".RoomStatus";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyRoom
    {
        public const string Default = GroupName + ".Room";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class ReservationRoom
    {
        public const string Default = GroupName + ".ReservationRoom";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyGuest
    {
        public const string Default = GroupName + ".Guest";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyReservationType
    {
        public const string Default = GroupName + ".ReservationType";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyReservationFoodAndBeverages
    {
        public const string Default = GroupName + ".ReservationFoodAndBeverages";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyReservation
    {
        public const string Default = GroupName + ".Reservation";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
        public const string CheckIn = Default + "CheckIn";
        public const string CheckOut = Default + "CheckOut";
        public const string RoomService = Default + "RoomService";
        public const string RoomFoodAndBeverage = Default + "RoomFoodAndBeverage";
    }

    public static class PolicyPayment
    {
        public const string Default = GroupName + ".Payment";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyPaymentDetails
    {
        public const string Default = GroupName + ".PaymentDetails";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyPaymentGuest
    {
        public const string Default = GroupName + ".PaymentGuest";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyFoodAndBeverageType
    {
        public const string Default = GroupName + ".FoodAndBeverageType";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyFoodAndBeverage
    {
        public const string Default = GroupName + ".FoodAndBeverage";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyServiceType
    {
        public const string Default = GroupName + ".ServiceType";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyService
    {
        public const string Default = GroupName + ".Service";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyMasterCompany
    {
        public const string Default = GroupName + ".MasterCompany";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyDiningOptions
    {
        public const string Default = GroupName + ".DiningOptions";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyMasterStatus
    {
        public const string Default = GroupName + ".MasterStatus";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyPaymentMethod
    {
        public const string Default = GroupName + ".PaymentMethod";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicySettings
    {
        public const string Default = GroupName + ".Settings";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyTax
    {
        public const string Default = GroupName + ".Tax";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyLostItem
    {
        public const string Default = GroupName + ".LostItem";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyReport
    {
        public const string Default = GroupName + ".Report";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
        public const string Export = Default + "Export";
        public const string Execute = Default + "Execute";
    }
}
