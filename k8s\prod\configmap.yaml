﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-prod
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  App__SelfUrl: "${PROD_APP_URL}"
  App__ClientUrl: "${PROD_CLIENT_URL}"
  App__CorsOrigins: "${PROD_CORS_ORIGINS}"
  App__AppName: "${PROD_APP_NAME}"
  AuthServer__Authority: "${PROD_APP_URL}"
  AuthServer__RequireHttpsMetadata: "true"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  ExternalAuth__ApiUrl: "${PROD_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  Redis__IsEnabled: "true"
  Redis__Configuration: "${PROD_REDIS_CONFIGURATION}"
  ActiveDirectory__Domain: "corp.imip.co.id"
  ActiveDirectory__LdapServer: "imaddc01.corp.imip.co.id"
  ActiveDirectory__BaseDn: "DC=corp,DC=imip,DC=co,DC=id"
  ActiveDirectory__Username: "<EMAIL>"
  ActiveDirectory__Port: "636"
  ActiveDirectory__UseSsl: "true"
  ActiveDirectory__Enabled: "true"
  ActiveDirectory__AutoLogin: "true"
  ActiveDirectory__WindowsAuthEnabled: "true"
  ActiveDirectory__WindowsAuthServiceUrl: "https://your-windows-auth-service.com"
  ActiveDirectory__WindowsAuthServiceApiKey: ""
  ActiveDirectory__WindowsAuthServiceTimeout: "30"
  RabbitMQ__Connections__Default__HostName: "${RABBITMQ_HOST}"
  RabbitMQ__Connections__Default__UserName: "${RABBITMQ_USERNAME}"
  RabbitMQ__Connections__Default__Password: "${RABBITMQ_PASSWORD}"
  RabbitMQ__Connections__Default__Port: "${RABBITMQ_PORT}"
  RabbitMQ__EventBus__ClientName: "IdentityServer-Prod"
  RabbitMQ__EventBus__ExchangeName: "${RABBITMQ_EXCHANGE_NAME}"
