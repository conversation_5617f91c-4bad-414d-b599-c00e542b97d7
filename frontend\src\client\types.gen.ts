// This file is auto-generated by @hey-api/openapi-ts

export type AbpLoginResult = {
    result?: LoginResultType;
    readonly description?: string | null;
};

export type ActionApiDescriptionModel = {
    uniqueName?: string | null;
    name?: string | null;
    httpMethod?: string | null;
    url?: string | null;
    supportedVersions?: Array<string> | null;
    parametersOnMethod?: Array<MethodParameterApiDescriptionModel> | null;
    parameters?: Array<ParameterApiDescriptionModel> | null;
    returnValue?: ReturnValueApiDescriptionModel;
    allowAnonymous?: boolean | null;
    implementFrom?: string | null;
};

export type ActiveDirectorySettingsDto = {
    enabled: boolean;
    domain: string;
    ldapServer: string;
    baseDn?: string | null;
    username: string;
    password: string;
    port: string;
    useSsl?: boolean;
    autoLogin?: boolean;
    tokenSecret?: string | null;
    defaultUsername?: string | null;
    windowsAuthEnabled?: boolean;
};

export type AddOpenIddictClientPropertyDto = {
    key?: string | null;
    value?: string | null;
};

export type ApiResponseOfAuditLogDto = {
    success?: boolean;
    message?: string | null;
    data?: AuditLogDto;
};

export type ApiResponseOfBoolean = {
    success?: boolean;
    message?: string | null;
    data?: boolean;
};

export type ApiResponseOfIdentityClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: IdentityClaimDto;
};

export type ApiResponseOfIdentityClaimTypeDto = {
    success?: boolean;
    message?: string | null;
    data?: IdentityClaimTypeDto;
};

export type ApiResponseOfImportResultDto = {
    success?: boolean;
    message?: string | null;
    data?: ImportResultDto;
};

export type ApiResponseOfListOfLabelValueDto1 = {
    success?: boolean;
    message?: string | null;
    data?: Array<LabelValueDtoOfString> | null;
};

export type ApiResponseOfListOfObject = {
    success?: boolean;
    message?: string | null;
    data?: Array<unknown> | null;
};

export type ApiResponseOfListOfOpenIddictAuthorizationDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<OpenIddictAuthorizationDto> | null;
};

export type ApiResponseOfListOfOpenIddictClientPropertyDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<OpenIddictClientPropertyDto> | null;
};

export type ApiResponseOfListOfOpenIddictClientSecretDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<OpenIddictClientSecretDto> | null;
};

export type ApiResponseOfListOfOpenIddictTokenDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<OpenIddictTokenDto> | null;
};

export type ApiResponseOfListOfRoleClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<RoleClaimDto> | null;
};

export type ApiResponseOfListOfString = {
    success?: boolean;
    message?: string | null;
    data?: Array<string> | null;
};

export type ApiResponseOfListOfUserClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: Array<UserClaimDto> | null;
};

export type ApiResponseOfOpenIddictApplicationDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictApplicationDto;
};

export type ApiResponseOfOpenIddictAuthorizationDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictAuthorizationDto;
};

export type ApiResponseOfOpenIddictClientPropertyDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictClientPropertyDto;
};

export type ApiResponseOfOpenIddictClientSecretDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictClientSecretDto;
};

export type ApiResponseOfOpenIddictResourceDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictResourceDto;
};

export type ApiResponseOfOpenIddictScopeDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictScopeDto;
};

export type ApiResponseOfOpenIddictTokenDto = {
    success?: boolean;
    message?: string | null;
    data?: OpenIddictTokenDto;
};

export type ApiResponseOfPagedResultDtoOfSecurityLogDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfSecurityLogDto;
};

export type ApiResponseOfRoleClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: RoleClaimDto;
};

export type ApiResponseOfSecurityLogDto = {
    success?: boolean;
    message?: string | null;
    data?: SecurityLogDto;
};

export type ApiResponseOfString = {
    success?: boolean;
    message?: string | null;
    data?: string | null;
};

export type ApiResponseOfUserClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: UserClaimDto;
};

export type ApplicationApiDescriptionModel = {
    modules?: {
        [key: string]: ModuleApiDescriptionModel;
    } | null;
    types?: {
        [key: string]: TypeApiDescriptionModel;
    } | null;
};

export type ApplicationAuthConfigurationDto = {
    grantedPolicies?: {
        [key: string]: boolean;
    } | null;
};

export type ApplicationConfigurationDto = {
    localization?: ApplicationLocalizationConfigurationDto;
    auth?: ApplicationAuthConfigurationDto;
    setting?: ApplicationSettingConfigurationDto;
    currentUser?: CurrentUserDto;
    features?: ApplicationFeatureConfigurationDto;
    globalFeatures?: ApplicationGlobalFeatureConfigurationDto;
    multiTenancy?: MultiTenancyInfoDto;
    currentTenant?: CurrentTenantDto;
    timing?: TimingDto;
    clock?: ClockDto;
    objectExtensions?: ObjectExtensionsDto;
    extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type ApplicationFeatureConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type ApplicationGlobalFeatureConfigurationDto = {
    enabledFeatures?: Array<string> | null;
};

export type ApplicationLocalizationConfigurationDto = {
    values?: {
        [key: string]: {
            [key: string]: string;
        };
    } | null;
    resources?: {
        [key: string]: ApplicationLocalizationResourceDto;
    } | null;
    languages?: Array<LanguageInfo> | null;
    currentCulture?: CurrentCultureDto;
    defaultResourceName?: string | null;
    languagesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
    languageFilesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
};

export type ApplicationLocalizationDto = {
    resources?: {
        [key: string]: ApplicationLocalizationResourceDto;
    } | null;
    currentCulture?: CurrentCultureDto;
};

export type ApplicationLocalizationResourceDto = {
    texts?: {
        [key: string]: string;
    } | null;
    baseResources?: Array<string> | null;
};

export type ApplicationSettingConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type AuditLogActionDto = {
    id?: string;
    serviceName?: string | null;
    methodName?: string | null;
    parameters?: string | null;
    executionTime?: string;
    executionDuration?: number;
};

export type AuditLogDto = {
    id?: string;
    applicationName?: string | null;
    userId?: string | null;
    userName?: string | null;
    executionTime?: string;
    executionDuration?: number;
    clientIpAddress?: string | null;
    clientName?: string | null;
    clientId?: string | null;
    correlationId?: string | null;
    browserInfo?: string | null;
    httpMethod?: string | null;
    url?: string | null;
    exceptions?: string | null;
    comments?: string | null;
    httpStatusCode?: number | null;
    actions?: Array<AuditLogActionDto> | null;
    entityChanges?: Array<EntityChangeDto> | null;
};

export type ChangePasswordInput = {
    currentPassword?: string | null;
    newPassword: string;
};

export type ClockDto = {
    kind?: string | null;
};

export type ConcurrentLoginPreventionDto = {
    userId?: string;
    concurrentLoginPreventionMode?: ConcurrentLoginPreventionMode;
};

export type ConcurrentLoginPreventionMode = 0 | 1 | 2;

export type ControllerApiDescriptionModel = {
    controllerName?: string | null;
    controllerGroupName?: string | null;
    isRemoteService?: boolean;
    isIntegrationService?: boolean;
    apiVersion?: string | null;
    type?: string | null;
    interfaces?: Array<ControllerInterfaceApiDescriptionModel> | null;
    actions?: {
        [key: string]: ActionApiDescriptionModel;
    } | null;
};

export type ControllerInterfaceApiDescriptionModel = {
    type?: string | null;
    name?: string | null;
    methods?: Array<InterfaceMethodApiDescriptionModel> | null;
};

export type CreateOpenIddictClientSecretDto = {
    type: string;
    value: string;
    description: string;
    expiration?: string | null;
};

export type CreateUpdateClaimDto = {
    claimType: string;
    claimValue: string;
    userId?: string | null;
};

export type CreateUpdateClaimTypeDto = {
    name: string;
    required?: boolean;
    regex?: string | null;
    regexDescription?: string | null;
    description?: string | null;
    isStatic?: boolean;
    valueType?: IdentityClaimValueType;
};

export type CreateUpdateOpenIddictApplicationDto = {
    applicationType: string;
    clientType: string;
    clientId: string;
    clientSecret?: string | null;
    displayName: string;
    clientUri: string;
    consentType?: string | null;
    redirectUris?: Array<string> | null;
    postLogoutRedirectUris?: Array<string> | null;
    permissions?: Array<string> | null;
    requirements?: Array<string> | null;
};

export type CreateUpdateOpenIddictResourceDto = {
    id?: string | null;
    name: string;
    displayName?: string | null;
    description?: string | null;
};

export type CreateUpdateOpenIddictScopeDto = {
    name: string;
    displayName: string;
    description?: string | null;
    resources?: Array<string> | null;
};

export type CurrentCultureDto = {
    displayName?: string | null;
    englishName?: string | null;
    threeLetterIsoLanguageName?: string | null;
    twoLetterIsoLanguageName?: string | null;
    isRightToLeft?: boolean;
    cultureName?: string | null;
    name?: string | null;
    nativeName?: string | null;
    dateTimeFormat?: DateTimeFormatDto;
};

export type CurrentTenantDto = {
    id?: string | null;
    name?: string | null;
    isAvailable?: boolean;
};

export type CurrentUserDto = {
    isAuthenticated?: boolean;
    id?: string | null;
    tenantId?: string | null;
    impersonatorUserId?: string | null;
    impersonatorTenantId?: string | null;
    impersonatorUserName?: string | null;
    impersonatorTenantName?: string | null;
    userName?: string | null;
    name?: string | null;
    surName?: string | null;
    email?: string | null;
    emailVerified?: boolean;
    phoneNumber?: string | null;
    phoneNumberVerified?: boolean;
    roles?: Array<string> | null;
    sessionId?: string | null;
};

export type DateTimeFormatDto = {
    calendarAlgorithmType?: string | null;
    dateTimeFormatLong?: string | null;
    shortDatePattern?: string | null;
    fullDateTimePattern?: string | null;
    dateSeparator?: string | null;
    shortTimePattern?: string | null;
    longTimePattern?: string | null;
};

export type EmailSettingsDto = {
    smtpHost?: string | null;
    smtpPort?: number;
    smtpUserName?: string | null;
    smtpPassword?: string | null;
    smtpDomain?: string | null;
    smtpEnableSsl?: boolean;
    smtpUseDefaultCredentials?: boolean;
    defaultFromAddress?: string | null;
    defaultFromDisplayName?: string | null;
};

export type EntityChangeDto = {
    id?: string;
    changeTime?: string;
    changeType?: EntityChangeType;
    entityId?: string | null;
    entityTypeFullName?: string | null;
    propertyChanges?: Array<EntityPropertyChangeDto> | null;
};

export type EntityChangeLogDto = {
    id?: string;
    changeTime?: string;
    changeType?: EntityChangeTypeDto;
    entityId?: string | null;
    entityTenantId?: string | null;
    entityTypeFullName?: string | null;
    entityDisplayName?: string | null;
    entityDisplayValue?: string | null;
    auditLogId?: string;
    userName?: string | null;
    propertyChanges?: Array<EntityPropertyChangeLogDto> | null;
};

export type EntityChangeType = 0 | 1 | 2;

export type EntityChangeTypeDto = 0 | 1 | 2;

export type EntityExtensionDto = {
    properties?: {
        [key: string]: ExtensionPropertyDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type EntityPropertyChangeDto = {
    id?: string;
    newValue?: string | null;
    originalValue?: string | null;
    propertyName?: string | null;
    propertyTypeFullName?: string | null;
};

export type EntityPropertyChangeLogDto = {
    id?: string;
    entityChangeLogId?: string;
    propertyName?: string | null;
    propertyDisplayName?: string | null;
    originalValue?: string | null;
    newValue?: string | null;
    propertyTypeFullName?: string | null;
    originalValueDisplay?: string | null;
    newValueDisplay?: string | null;
    isReference?: boolean;
};

export type ExtensionEnumDto = {
    fields?: Array<ExtensionEnumFieldDto> | null;
    localizationResource?: string | null;
};

export type ExtensionEnumFieldDto = {
    name?: string | null;
    value?: unknown;
};

export type ExtensionPropertyApiCreateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiDto = {
    onGet?: ExtensionPropertyApiGetDto;
    onCreate?: ExtensionPropertyApiCreateDto;
    onUpdate?: ExtensionPropertyApiUpdateDto;
};

export type ExtensionPropertyApiGetDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiUpdateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyAttributeDto = {
    typeSimple?: string | null;
    config?: {
        [key: string]: unknown;
    } | null;
};

export type ExtensionPropertyDto = {
    type?: string | null;
    typeSimple?: string | null;
    displayName?: LocalizableStringDto;
    api?: ExtensionPropertyApiDto;
    ui?: ExtensionPropertyUiDto;
    policy?: ExtensionPropertyPolicyDto;
    attributes?: Array<ExtensionPropertyAttributeDto> | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
    defaultValue?: unknown;
};

export type ExtensionPropertyFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyGlobalFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPermissionPolicyDto = {
    permissionNames?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPolicyDto = {
    globalFeatures?: ExtensionPropertyGlobalFeaturePolicyDto;
    features?: ExtensionPropertyFeaturePolicyDto;
    permissions?: ExtensionPropertyPermissionPolicyDto;
};

export type ExtensionPropertyUiDto = {
    onTable?: ExtensionPropertyUiTableDto;
    onCreateForm?: ExtensionPropertyUiFormDto;
    onEditForm?: ExtensionPropertyUiFormDto;
    lookup?: ExtensionPropertyUiLookupDto;
};

export type ExtensionPropertyUiFormDto = {
    isVisible?: boolean;
};

export type ExtensionPropertyUiLookupDto = {
    url?: string | null;
    resultListPropertyName?: string | null;
    displayPropertyName?: string | null;
    valuePropertyName?: string | null;
    filterParamName?: string | null;
};

export type ExtensionPropertyUiTableDto = {
    isVisible?: boolean;
};

export type FeatureDto = {
    name?: string | null;
    displayName?: string | null;
    value?: string | null;
    provider?: FeatureProviderDto;
    description?: string | null;
    valueType?: IStringValueType;
    depth?: number;
    parentName?: string | null;
};

export type FeatureGroupDto = {
    name?: string | null;
    displayName?: string | null;
    features?: Array<FeatureDto> | null;
};

export type FeatureProviderDto = {
    name?: string | null;
    key?: string | null;
};

export type FilterCondition = {
    fieldName: string;
    operator: FilterOperator;
    value: unknown;
};

export type FilterGroup = {
    operator: LogicalOperator;
    conditions: Array<FilterCondition>;
};

export type FilterOperator = 'Equals' | 'NotEquals' | 'Contains' | 'StartsWith' | 'EndsWith' | 'GreaterThan' | 'GreaterThanOrEqual' | 'LessThan' | 'LessThanOrEqual' | 'In' | 'NotIn' | 'Between' | 'NotBetween' | 'IsNull' | 'IsNotNull' | 'IsEmpty' | 'IsNotEmpty' | 'IsTrue' | 'IsFalse' | 'IsNullOrEmpty' | 'IsNotNullOrEmpty' | 'IsNullOrWhiteSpace' | 'IsNotNullOrWhiteSpace' | 'IsNumeric' | 'IsAlpha' | 'IsAlphaNumeric' | 'IsEmail' | 'IsUrl' | 'IsIp' | 'IsIpv4' | 'IsIpv6' | 'IsGuid' | 'IsGuidEmpty' | 'IsGuidNotEmpty' | 'IsGuidNull' | 'IsGuidNotNull' | 'IsGuidNullOrEmpty' | 'IsGuidNotNullOrEmpty' | 'IsGuidNullOrWhiteSpace' | 'IsGuidNotNullOrWhiteSpace' | 'IsGuidNumeric' | 'IsGuidAlpha' | 'IsGuidAlphaNumeric';

export type FindTenantResultDto = {
    success?: boolean;
    tenantId?: string | null;
    name?: string | null;
    normalizedName?: string | null;
    isActive?: boolean;
};

export type GetFeatureListResultDto = {
    groups?: Array<FeatureGroupDto> | null;
};

export type GetPermissionListResultDto = {
    entityDisplayName?: string | null;
    groups?: Array<PermissionGroupDto> | null;
};

export type GetSecurityLogsInput = {
    sorting?: string | null;
    page?: number;
    sort?: Array<SortInfo> | null;
    filterGroup?: FilterGroup;
    readonly skipCount?: number;
    maxResultCount?: number;
    startDate?: string | null;
    endDate?: string | null;
    applicationName?: string | null;
    identity?: string | null;
    action?: string | null;
    userId?: string | null;
    userName?: string | null;
    clientIpAddress?: string | null;
    correlationId?: string | null;
};

export type IStringValueType = {
    readonly name?: string | null;
    readonly properties?: {
        [key: string]: unknown;
    } | null;
    validator?: IValueValidator;
};

export type IValueValidator = {
    readonly name?: string | null;
    readonly properties?: {
        [key: string]: unknown;
    } | null;
};

export type IanaTimeZone = {
    timeZoneName?: string | null;
};

export type IdentityClaimDto = {
    id?: string;
    claimType?: string | null;
    claimValue?: string | null;
    userId?: string;
};

export type IdentityClaimTypeDto = {
    id?: string;
    name: string | null;
    required?: boolean;
    isStatic?: boolean;
    regex?: string | null;
    regexDescription?: string | null;
    description?: string | null;
    valueType?: IdentityClaimValueType;
};

export type IdentityClaimValueType = 0 | 1 | 2 | 3;

export type IdentityRoleCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
};

export type IdentityRoleDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    isDefault?: boolean;
    isStatic?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
    creationTime?: string;
};

export type IdentityRoleUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    isDefault?: boolean;
    isPublic?: boolean;
    concurrencyStamp?: string | null;
};

export type IdentityUpdateApplicationGrantTypesDto = {
    grantTypes: Array<string>;
};

export type IdentityUserCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password: string;
};

export type IdentityUserDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type IdentityUserUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    name?: string | null;
    surname?: string | null;
    email: string;
    phoneNumber?: string | null;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    roleNames?: Array<string> | null;
    password?: string | null;
    concurrencyStamp?: string | null;
};

export type IdentityUserUpdateRolesDto = {
    roleNames: Array<string>;
};

export type ImportResultDto = {
    rolesImported?: number;
    usersImported?: number;
    userRolesImported?: number;
    errors?: Array<string> | null;
};

export type InterfaceMethodApiDescriptionModel = {
    name?: string | null;
    parametersOnMethod?: Array<MethodParameterApiDescriptionModel> | null;
    returnValue?: ReturnValueApiDescriptionModel;
};

export type LabelValueDtoOfString = {
    label?: string | null;
    value?: string | null;
};

export type LanguageInfo = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
    readonly twoLetterISOLanguageName?: string | null;
};

export type ListResultDtoOfIdentityRoleDto = {
    items?: Array<IdentityRoleDto> | null;
};

export type ListResultDtoOfUserData = {
    items?: Array<UserData> | null;
};

export type LocalizableStringDto = {
    name?: string | null;
    resource?: string | null;
};

export type LogicalOperator = 'And' | 'Or';

export type LoginResultType = 1 | 2 | 3 | 4 | 5;

export type MetaData = {
    page?: number;
    pageSize?: number;
    totalPages?: number;
    totalItems?: number;
};

export type MethodParameterApiDescriptionModel = {
    name?: string | null;
    typeAsString?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isOptional?: boolean;
    defaultValue?: unknown;
};

export type ModuleApiDescriptionModel = {
    rootPath?: string | null;
    remoteServiceName?: string | null;
    controllers?: {
        [key: string]: ControllerApiDescriptionModel;
    } | null;
};

export type ModuleExtensionDto = {
    entities?: {
        [key: string]: EntityExtensionDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type MultiTenancyInfoDto = {
    isEnabled?: boolean;
};

export type NameValue = {
    name?: string | null;
    value?: string | null;
};

export type ObjectExtensionsDto = {
    modules?: {
        [key: string]: ModuleExtensionDto;
    } | null;
    enums?: {
        [key: string]: ExtensionEnumDto;
    } | null;
};

export type OpenIddictApplicationDto = {
    id?: string;
    applicationType: string | null;
    clientType: string | null;
    clientId: string | null;
    clientSecret?: string | null;
    displayName?: string | null;
    clientUri?: string | null;
    consentType?: string | null;
    redirectUris?: Array<string> | null;
    postLogoutRedirectUris?: Array<string> | null;
    permissions?: Array<string> | null;
    requirements?: Array<string> | null;
};

export type OpenIddictAuthorizationDto = {
    id?: string;
    applicationId?: string | null;
    applicationName?: string | null;
    clientId?: string | null;
    subject?: string | null;
    type?: string | null;
    status?: string | null;
    creationDate?: string | null;
    scopes?: Array<string> | null;
};

export type OpenIddictClientPropertyDto = {
    key?: string | null;
    value?: string | null;
};

export type OpenIddictClientSecretDto = {
    id?: string;
    type: string;
    value: string;
    description: string;
    expiration?: string | null;
    creationDate?: string;
};

export type OpenIddictResourceDto = {
    id?: string;
    name?: string | null;
    displayName?: string | null;
    description?: string | null;
};

export type OpenIddictScopeDto = {
    id?: string;
    name?: string | null;
    displayName?: string | null;
    description?: string | null;
    resources?: Array<string> | null;
};

export type OpenIddictTokenDto = {
    id?: string | null;
    clientId?: string | null;
    subject?: string | null;
    type?: string | null;
    status?: string | null;
    creationDate?: string | null;
    expirationDate?: string | null;
};

export type PagedApiResponseOfPagedResultDtoOfAuditLogDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfAuditLogDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfIdentityClaimDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfIdentityClaimDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfIdentityClaimTypeDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfIdentityClaimTypeDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfOpenIddictApplicationDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfOpenIddictApplicationDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfOpenIddictAuthorizationDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfOpenIddictAuthorizationDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfOpenIddictResourceDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfOpenIddictResourceDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfOpenIddictScopeDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfOpenIddictScopeDto;
    meta?: MetaData;
};

export type PagedApiResponseOfPagedResultDtoOfSecurityLogDto = {
    success?: boolean;
    message?: string | null;
    data?: PagedResultDtoOfSecurityLogDto;
    meta?: MetaData;
};

export type PagedResultDtoOfAuditLogDto = {
    items?: Array<AuditLogDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfIdentityClaimDto = {
    items?: Array<IdentityClaimDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfIdentityClaimTypeDto = {
    items?: Array<IdentityClaimTypeDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfIdentityRoleDto = {
    items?: Array<IdentityRoleDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfIdentityUserDto = {
    items?: Array<IdentityUserDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfOpenIddictApplicationDto = {
    items?: Array<OpenIddictApplicationDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfOpenIddictAuthorizationDto = {
    items?: Array<OpenIddictAuthorizationDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfOpenIddictResourceDto = {
    items?: Array<OpenIddictResourceDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfOpenIddictScopeDto = {
    items?: Array<OpenIddictScopeDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfSecurityLogDto = {
    items?: Array<SecurityLogDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTenantDto = {
    items?: Array<TenantDto> | null;
    totalCount?: number;
};

export type ParameterApiDescriptionModel = {
    nameOnMethod?: string | null;
    name?: string | null;
    jsonName?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isOptional?: boolean;
    defaultValue?: unknown;
    constraintTypes?: Array<string> | null;
    bindingSourceId?: string | null;
    descriptorName?: string | null;
};

export type PermissionGrantInfoDto = {
    name?: string | null;
    displayName?: string | null;
    parentName?: string | null;
    isGranted?: boolean;
    allowedProviders?: Array<string> | null;
    grantedProviders?: Array<ProviderInfoDto> | null;
};

export type PermissionGroupDto = {
    name?: string | null;
    displayName?: string | null;
    displayNameKey?: string | null;
    displayNameResource?: string | null;
    permissions?: Array<PermissionGrantInfoDto> | null;
};

export type ProblemDetails = {
    type?: string | null;
    title?: string | null;
    status?: number | null;
    detail?: string | null;
    instance?: string | null;
    [key: string]: unknown | (string | null) | (string | null) | (number | null) | (string | null) | (string | null) | undefined;
};

export type ProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type PropertyApiDescriptionModel = {
    name?: string | null;
    jsonName?: string | null;
    type?: string | null;
    typeSimple?: string | null;
    isRequired?: boolean;
    minLength?: number | null;
    maxLength?: number | null;
    minimum?: string | null;
    maximum?: string | null;
    regex?: string | null;
};

export type ProviderInfoDto = {
    providerName?: string | null;
    providerKey?: string | null;
};

export type QueryParameters = {
    sorting?: string | null;
    page?: number;
    sort?: Array<SortInfo> | null;
    filterGroup?: FilterGroup;
    readonly skipCount?: number;
    maxResultCount?: number;
};

export type RegisterDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type RemoteServiceErrorInfo = {
    code?: string | null;
    message?: string | null;
    details?: string | null;
    data?: {
        [key: string]: unknown;
    } | null;
    validationErrors?: Array<RemoteServiceValidationErrorInfo> | null;
};

export type RemoteServiceErrorResponse = {
    error?: RemoteServiceErrorInfo;
};

export type RemoteServiceValidationErrorInfo = {
    message?: string | null;
    members?: Array<string> | null;
};

export type ResetPasswordDto = {
    userId?: string;
    resetToken: string;
    password: string;
};

export type ReturnValueApiDescriptionModel = {
    type?: string | null;
    typeSimple?: string | null;
};

export type RevokeAuthorizationDto = {
    id?: string;
};

export type RevokeTokenDto = {
    tokenId?: string | null;
};

export type RoleClaimDto = {
    id?: string;
    roleId?: string;
    claimType?: string | null;
    claimValue?: string | null;
};

export type SecurityLogDto = {
    id?: string;
    applicationName?: string | null;
    identity?: string | null;
    action?: string | null;
    userId?: string | null;
    userName?: string | null;
    tenantName?: string | null;
    clientId?: string | null;
    correlationId?: string | null;
    clientIpAddress?: string | null;
    browserInfo?: string | null;
    creationTime?: string;
};

export type SendPasswordResetCodeDto = {
    email: string;
    appName: string;
    returnUrl?: string | null;
    returnUrlHash?: string | null;
};

export type SendTestEmailInput = {
    senderEmailAddress: string;
    targetEmailAddress: string;
    subject: string;
    body?: string | null;
};

export type SortInfo = {
    field?: string | null;
    desc?: boolean;
};

export type TenantCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type TenantDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type TenantUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    concurrencyStamp?: string | null;
};

export type TimeZone = {
    iana?: IanaTimeZone;
    windows?: WindowsTimeZone;
};

export type TimingDto = {
    timeZone?: TimeZone;
};

export type TypeApiDescriptionModel = {
    baseType?: string | null;
    isEnum?: boolean;
    enumNames?: Array<string> | null;
    enumValues?: Array<unknown> | null;
    genericArguments?: Array<string> | null;
    properties?: Array<PropertyApiDescriptionModel> | null;
};

export type UpdateApplicationScopesDto = {
    scopes?: Array<string> | null;
};

export type UpdateEmailSettingsDto = {
    smtpHost?: string | null;
    smtpPort?: number;
    smtpUserName?: string | null;
    smtpPassword?: string | null;
    smtpDomain?: string | null;
    smtpEnableSsl?: boolean;
    smtpUseDefaultCredentials?: boolean;
    defaultFromAddress: string;
    defaultFromDisplayName: string;
};

export type UpdateFeatureDto = {
    name?: string | null;
    value?: string | null;
};

export type UpdateFeaturesDto = {
    features?: Array<UpdateFeatureDto> | null;
};

export type UpdateOpenIddictClientPropertyDto = {
    value?: string | null;
};

export type UpdatePermissionDto = {
    name?: string | null;
    isGranted?: boolean;
};

export type UpdatePermissionsDto = {
    permissions?: Array<UpdatePermissionDto> | null;
};

export type UpdateProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type UpdateScopeResourcesDto = {
    resources: Array<string>;
};

export type UserClaimDto = {
    id?: string;
    userId?: string;
    claimType?: string | null;
    claimValue?: string | null;
};

export type UserData = {
    id?: string;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    isActive?: boolean;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type UserLoginInfo = {
    userNameOrEmailAddress: string;
    password: string;
    rememberMe?: boolean;
};

export type VerifyPasswordResetTokenInput = {
    userId?: string;
    resetToken: string;
};

export type WindowsTimeZone = {
    timeZoneId?: string | null;
};

export type WindowsUserDetailsResponse = {
    username?: string | null;
    fullUsername?: string | null;
    displayName?: string | null;
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    description?: string | null;
    distinguishedName?: string | null;
    userPrincipalName?: string | null;
    samAccountName?: string | null;
    isEnabled?: boolean | null;
    lastLogon?: string | null;
    lastPasswordSet?: string | null;
    groups?: Array<string> | null;
    timestamp?: string;
};

export type WindowsUserResponse = {
    username?: string | null;
    fullUsername?: string | null;
    isAuthenticated?: boolean;
    authType?: string | null;
    timestamp?: string;
};

export type GetApiAbpApiDefinitionData = {
    body?: never;
    path?: never;
    query?: {
        IncludeTypes?: boolean;
    };
    url: '/api/abp/api-definition';
};

export type GetApiAbpApiDefinitionErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApiDefinitionError = GetApiAbpApiDefinitionErrors[keyof GetApiAbpApiDefinitionErrors];

export type GetApiAbpApiDefinitionResponses = {
    /**
     * OK
     */
    200: ApplicationApiDescriptionModel;
};

export type GetApiAbpApiDefinitionResponse = GetApiAbpApiDefinitionResponses[keyof GetApiAbpApiDefinitionResponses];

export type GetApiAbpApplicationConfigurationData = {
    body?: never;
    path?: never;
    query?: {
        IncludeLocalizationResources?: boolean;
    };
    url: '/api/abp/application-configuration';
};

export type GetApiAbpApplicationConfigurationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError = GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
    /**
     * OK
     */
    200: ApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse = GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpApplicationLocalizationData = {
    body?: never;
    path?: never;
    query: {
        CultureName: string;
        OnlyDynamics?: boolean;
    };
    url: '/api/abp/application-localization';
};

export type GetApiAbpApplicationLocalizationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationLocalizationError = GetApiAbpApplicationLocalizationErrors[keyof GetApiAbpApplicationLocalizationErrors];

export type GetApiAbpApplicationLocalizationResponses = {
    /**
     * OK
     */
    200: ApplicationLocalizationDto;
};

export type GetApiAbpApplicationLocalizationResponse = GetApiAbpApplicationLocalizationResponses[keyof GetApiAbpApplicationLocalizationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-name/{name}';
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError = GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse = GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-id/{id}';
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError = GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse = GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type PostApiAccountRegisterData = {
    body?: RegisterDto;
    path?: never;
    query?: never;
    url: '/api/account/register';
};

export type PostApiAccountRegisterErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError = PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PostApiAccountRegisterResponse = PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
    body?: SendPasswordResetCodeDto;
    path?: never;
    query?: never;
    url: '/api/account/send-password-reset-code';
};

export type PostApiAccountSendPasswordResetCodeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError = PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
    body?: VerifyPasswordResetTokenInput;
    path?: never;
    query?: never;
    url: '/api/account/verify-password-reset-token';
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError = PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse = PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
    body?: ResetPasswordDto;
    path?: never;
    query?: never;
    url: '/api/account/reset-password';
};

export type PostApiAccountResetPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError = PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiActiveDirectorySettingsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/active-directory-settings';
};

export type GetApiActiveDirectorySettingsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiActiveDirectorySettingsError = GetApiActiveDirectorySettingsErrors[keyof GetApiActiveDirectorySettingsErrors];

export type GetApiActiveDirectorySettingsResponses = {
    /**
     * OK
     */
    200: ActiveDirectorySettingsDto;
};

export type GetApiActiveDirectorySettingsResponse = GetApiActiveDirectorySettingsResponses[keyof GetApiActiveDirectorySettingsResponses];

export type PutApiActiveDirectorySettingsData = {
    body?: ActiveDirectorySettingsDto;
    path?: never;
    query?: never;
    url: '/api/active-directory-settings';
};

export type PutApiActiveDirectorySettingsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiActiveDirectorySettingsError = PutApiActiveDirectorySettingsErrors[keyof PutApiActiveDirectorySettingsErrors];

export type PutApiActiveDirectorySettingsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiActiveDirectorySettingsTestConnectionData = {
    body?: ActiveDirectorySettingsDto;
    path?: never;
    query?: never;
    url: '/api/active-directory-settings/test-connection';
};

export type PostApiActiveDirectorySettingsTestConnectionErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiActiveDirectorySettingsTestConnectionError = PostApiActiveDirectorySettingsTestConnectionErrors[keyof PostApiActiveDirectorySettingsTestConnectionErrors];

export type PostApiActiveDirectorySettingsTestConnectionResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiActiveDirectorySettingsTestConnectionResponse = PostApiActiveDirectorySettingsTestConnectionResponses[keyof PostApiActiveDirectorySettingsTestConnectionResponses];

export type GetApiIdpActiveDirectorySettingsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/idp/active-directory-settings';
};

export type GetApiIdpActiveDirectorySettingsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdpActiveDirectorySettingsError = GetApiIdpActiveDirectorySettingsErrors[keyof GetApiIdpActiveDirectorySettingsErrors];

export type GetApiIdpActiveDirectorySettingsResponses = {
    /**
     * OK
     */
    200: ActiveDirectorySettingsDto;
};

export type GetApiIdpActiveDirectorySettingsResponse = GetApiIdpActiveDirectorySettingsResponses[keyof GetApiIdpActiveDirectorySettingsResponses];

export type PutApiIdpActiveDirectorySettingsData = {
    body?: ActiveDirectorySettingsDto;
    path?: never;
    query?: never;
    url: '/api/idp/active-directory-settings';
};

export type PutApiIdpActiveDirectorySettingsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdpActiveDirectorySettingsError = PutApiIdpActiveDirectorySettingsErrors[keyof PutApiIdpActiveDirectorySettingsErrors];

export type PutApiIdpActiveDirectorySettingsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdpActiveDirectorySettingsTestConnectionData = {
    body?: ActiveDirectorySettingsDto;
    path?: never;
    query?: never;
    url: '/api/idp/active-directory-settings/test-connection';
};

export type PostApiIdpActiveDirectorySettingsTestConnectionErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdpActiveDirectorySettingsTestConnectionError = PostApiIdpActiveDirectorySettingsTestConnectionErrors[keyof PostApiIdpActiveDirectorySettingsTestConnectionErrors];

export type PostApiIdpActiveDirectorySettingsTestConnectionResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiIdpActiveDirectorySettingsTestConnectionResponse = PostApiIdpActiveDirectorySettingsTestConnectionResponses[keyof PostApiIdpActiveDirectorySettingsTestConnectionResponses];

export type GetAdminClaimsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/claims';
};

export type GetAdminClaimsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminUsersData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/users';
};

export type GetAdminUsersResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminUsersRolesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/users/roles';
};

export type GetAdminUsersRolesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminClientsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/clients';
};

export type GetAdminClientsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminClientsResourcesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/clients/resources';
};

export type GetAdminClientsResourcesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminClientsScopesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/clients/scopes';
};

export type GetAdminClientsScopesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminTenantsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/tenants';
};

export type GetAdminTenantsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminSettingsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin/settings';
};

export type GetAdminSettingsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAuditLogsListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/audit-logs/list';
};

export type PostApiAuditLogsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiAuditLogsListError = PostApiAuditLogsListErrors[keyof PostApiAuditLogsListErrors];

export type PostApiAuditLogsListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfAuditLogDto;
};

export type PostApiAuditLogsListResponse = PostApiAuditLogsListResponses[keyof PostApiAuditLogsListResponses];

export type GetApiAuditLogsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/audit-logs/{id}';
};

export type GetApiAuditLogsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiAuditLogsByIdError = GetApiAuditLogsByIdErrors[keyof GetApiAuditLogsByIdErrors];

export type GetApiAuditLogsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfAuditLogDto;
};

export type GetApiAuditLogsByIdResponse = GetApiAuditLogsByIdResponses[keyof GetApiAuditLogsByIdResponses];

export type PostApiIdentityClaimTypesListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/identity/claim-types/list';
};

export type PostApiIdentityClaimTypesListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityClaimTypesListError = PostApiIdentityClaimTypesListErrors[keyof PostApiIdentityClaimTypesListErrors];

export type PostApiIdentityClaimTypesListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfIdentityClaimTypeDto;
};

export type PostApiIdentityClaimTypesListResponse = PostApiIdentityClaimTypesListResponses[keyof PostApiIdentityClaimTypesListResponses];

export type DeleteApiIdentityClaimTypesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claim-types/{id}';
};

export type DeleteApiIdentityClaimTypesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiIdentityClaimTypesByIdError = DeleteApiIdentityClaimTypesByIdErrors[keyof DeleteApiIdentityClaimTypesByIdErrors];

export type DeleteApiIdentityClaimTypesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiIdentityClaimTypesByIdResponse = DeleteApiIdentityClaimTypesByIdResponses[keyof DeleteApiIdentityClaimTypesByIdResponses];

export type GetApiIdentityClaimTypesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claim-types/{id}';
};

export type GetApiIdentityClaimTypesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityClaimTypesByIdError = GetApiIdentityClaimTypesByIdErrors[keyof GetApiIdentityClaimTypesByIdErrors];

export type GetApiIdentityClaimTypesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimTypeDto;
};

export type GetApiIdentityClaimTypesByIdResponse = GetApiIdentityClaimTypesByIdResponses[keyof GetApiIdentityClaimTypesByIdResponses];

export type PutApiIdentityClaimTypesByIdData = {
    body?: CreateUpdateClaimTypeDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claim-types/{id}';
};

export type PutApiIdentityClaimTypesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiIdentityClaimTypesByIdError = PutApiIdentityClaimTypesByIdErrors[keyof PutApiIdentityClaimTypesByIdErrors];

export type PutApiIdentityClaimTypesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimTypeDto;
};

export type PutApiIdentityClaimTypesByIdResponse = PutApiIdentityClaimTypesByIdResponses[keyof PutApiIdentityClaimTypesByIdResponses];

export type PostApiIdentityClaimTypesData = {
    body?: CreateUpdateClaimTypeDto;
    path?: never;
    query?: never;
    url: '/api/identity/claim-types';
};

export type PostApiIdentityClaimTypesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityClaimTypesError = PostApiIdentityClaimTypesErrors[keyof PostApiIdentityClaimTypesErrors];

export type PostApiIdentityClaimTypesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimTypeDto;
};

export type PostApiIdentityClaimTypesResponse = PostApiIdentityClaimTypesResponses[keyof PostApiIdentityClaimTypesResponses];

export type PostApiAccountDynamicClaimsRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/dynamic-claims/refresh';
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError = PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementEmailingData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing';
};

export type GetApiSettingManagementEmailingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiSettingManagementEmailingError = GetApiSettingManagementEmailingErrors[keyof GetApiSettingManagementEmailingErrors];

export type GetApiSettingManagementEmailingResponses = {
    /**
     * OK
     */
    200: EmailSettingsDto;
};

export type GetApiSettingManagementEmailingResponse = GetApiSettingManagementEmailingResponses[keyof GetApiSettingManagementEmailingResponses];

export type PostApiSettingManagementEmailingData = {
    body?: UpdateEmailSettingsDto;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing';
};

export type PostApiSettingManagementEmailingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiSettingManagementEmailingError = PostApiSettingManagementEmailingErrors[keyof PostApiSettingManagementEmailingErrors];

export type PostApiSettingManagementEmailingResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiSettingManagementEmailingSendTestEmailData = {
    body?: SendTestEmailInput;
    path?: never;
    query?: never;
    url: '/api/setting-management/emailing/send-test-email';
};

export type PostApiSettingManagementEmailingSendTestEmailErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiSettingManagementEmailingSendTestEmailError = PostApiSettingManagementEmailingSendTestEmailErrors[keyof PostApiSettingManagementEmailingSendTestEmailErrors];

export type PostApiSettingManagementEmailingSendTestEmailResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogData = {
    body?: never;
    path?: never;
    query?: {
        startTime?: string;
        endTime?: string;
        maxResultCount?: number;
        skipCount?: number;
    };
    url: '/api/change-log';
};

export type GetApiChangeLogResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogEntityData = {
    body?: never;
    path?: never;
    query?: {
        EntityId?: string;
        EntityTypeFullName?: string;
        StartTime?: string;
        EndTime?: string;
        ChangeType?: EntityChangeTypeDto;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/change-log/entity';
};

export type GetApiChangeLogEntityResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogEntityLatestData = {
    body?: never;
    path?: never;
    query?: {
        entityId?: string;
        entityTypeFullName?: string;
    };
    url: '/api/change-log/entity/latest';
};

export type GetApiChangeLogEntityLatestResponses = {
    /**
     * OK
     */
    200: EntityChangeLogDto;
};

export type GetApiChangeLogEntityLatestResponse = GetApiChangeLogEntityLatestResponses[keyof GetApiChangeLogEntityLatestResponses];

export type GetApiChangeLogEntityTypeData = {
    body?: never;
    path?: never;
    query?: {
        entityTypeFullName?: string;
        maxResultCount?: number;
        skipCount?: number;
    };
    url: '/api/change-log/entity-type';
};

export type GetApiChangeLogEntityTypeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogTimeRangeData = {
    body?: never;
    path?: never;
    query?: {
        startTime?: string;
        endTime?: string;
        maxResultCount?: number;
        skipCount?: number;
    };
    url: '/api/change-log/time-range';
};

export type GetApiChangeLogTimeRangeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogPropertyHistoryData = {
    body?: never;
    path?: never;
    query?: {
        entityId?: string;
        entityTypeFullName?: string;
        propertyName?: string;
        maxResultCount?: number;
        skipCount?: number;
    };
    url: '/api/change-log/property-history';
};

export type GetApiChangeLogPropertyHistoryResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiChangeLogEntityChangesData = {
    body?: never;
    path?: never;
    query?: {
        entityId?: string;
        entityTypeFullName?: string;
        startTime?: string;
        endTime?: string;
        maxResultCount?: number;
        skipCount?: number;
        sorting?: string;
    };
    url: '/api/change-log/entity-changes';
};

export type GetApiChangeLogEntityChangesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type DeleteApiFeatureManagementFeaturesData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type DeleteApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiFeatureManagementFeaturesError = DeleteApiFeatureManagementFeaturesErrors[keyof DeleteApiFeatureManagementFeaturesErrors];

export type DeleteApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiFeatureManagementFeaturesData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type GetApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiFeatureManagementFeaturesError = GetApiFeatureManagementFeaturesErrors[keyof GetApiFeatureManagementFeaturesErrors];

export type GetApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: GetFeatureListResultDto;
};

export type GetApiFeatureManagementFeaturesResponse = GetApiFeatureManagementFeaturesResponses[keyof GetApiFeatureManagementFeaturesResponses];

export type PutApiFeatureManagementFeaturesData = {
    body?: UpdateFeaturesDto;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/feature-management/features';
};

export type PutApiFeatureManagementFeaturesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiFeatureManagementFeaturesError = PutApiFeatureManagementFeaturesErrors[keyof PutApiFeatureManagementFeaturesErrors];

export type PutApiFeatureManagementFeaturesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiHealthKubernetesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/health/kubernetes';
};

export type GetApiHealthKubernetesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/';
};

export type GetResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetAdminData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/admin';
};

export type GetAdminResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiIdentityClaimsListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/identity/claims/list';
};

export type PostApiIdentityClaimsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityClaimsListError = PostApiIdentityClaimsListErrors[keyof PostApiIdentityClaimsListErrors];

export type PostApiIdentityClaimsListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfIdentityClaimDto;
};

export type PostApiIdentityClaimsListResponse = PostApiIdentityClaimsListResponses[keyof PostApiIdentityClaimsListResponses];

export type DeleteApiIdentityClaimsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claims/{id}';
};

export type DeleteApiIdentityClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiIdentityClaimsByIdError = DeleteApiIdentityClaimsByIdErrors[keyof DeleteApiIdentityClaimsByIdErrors];

export type DeleteApiIdentityClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiIdentityClaimsByIdResponse = DeleteApiIdentityClaimsByIdResponses[keyof DeleteApiIdentityClaimsByIdResponses];

export type GetApiIdentityClaimsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claims/{id}';
};

export type GetApiIdentityClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityClaimsByIdError = GetApiIdentityClaimsByIdErrors[keyof GetApiIdentityClaimsByIdErrors];

export type GetApiIdentityClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimDto;
};

export type GetApiIdentityClaimsByIdResponse = GetApiIdentityClaimsByIdResponses[keyof GetApiIdentityClaimsByIdResponses];

export type PutApiIdentityClaimsByIdData = {
    body?: CreateUpdateClaimDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/claims/{id}';
};

export type PutApiIdentityClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiIdentityClaimsByIdError = PutApiIdentityClaimsByIdErrors[keyof PutApiIdentityClaimsByIdErrors];

export type PutApiIdentityClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimDto;
};

export type PutApiIdentityClaimsByIdResponse = PutApiIdentityClaimsByIdResponses[keyof PutApiIdentityClaimsByIdResponses];

export type PostApiIdentityClaimsData = {
    body?: CreateUpdateClaimDto;
    path?: never;
    query?: never;
    url: '/api/identity/claims';
};

export type PostApiIdentityClaimsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityClaimsError = PostApiIdentityClaimsErrors[keyof PostApiIdentityClaimsErrors];

export type PostApiIdentityClaimsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfIdentityClaimDto;
};

export type PostApiIdentityClaimsResponse = PostApiIdentityClaimsResponses[keyof PostApiIdentityClaimsResponses];

export type PostApiImportCsvData = {
    body?: {
        File: Blob | File;
        Type: string;
        Delimiter?: string;
    };
    path?: never;
    query?: never;
    url: '/api/import/csv';
};

export type PostApiImportCsvErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiImportCsvError = PostApiImportCsvErrors[keyof PostApiImportCsvErrors];

export type PostApiImportCsvResponses = {
    /**
     * OK
     */
    200: ApiResponseOfImportResultDto;
};

export type PostApiImportCsvResponse = PostApiImportCsvResponses[keyof PostApiImportCsvResponses];

export type GetApiImportTemplatesData = {
    body?: never;
    path?: never;
    query?: {
        type?: string;
    };
    url: '/api/import/templates';
};

export type GetApiImportTemplatesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiImportTemplatesError = GetApiImportTemplatesErrors[keyof GetApiImportTemplatesErrors];

export type GetApiImportTemplatesResponses = {
    /**
     * OK
     */
    200: Blob | File;
};

export type GetApiImportTemplatesResponse = GetApiImportTemplatesResponses[keyof GetApiImportTemplatesResponses];

export type PostApiAccountLoginData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/login';
};

export type PostApiAccountLoginErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountLoginError = PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountLoginResponse = PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/logout';
};

export type GetApiAccountLogoutErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError = GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountCheckPasswordData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/check-password';
};

export type PostApiAccountCheckPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError = PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse = PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type PostApiOpeniddictApplicationsListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications/list';
};

export type PostApiOpeniddictApplicationsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictApplicationsListError = PostApiOpeniddictApplicationsListErrors[keyof PostApiOpeniddictApplicationsListErrors];

export type PostApiOpeniddictApplicationsListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfOpenIddictApplicationDto;
};

export type PostApiOpeniddictApplicationsListResponse = PostApiOpeniddictApplicationsListResponses[keyof PostApiOpeniddictApplicationsListResponses];

export type DeleteApiOpeniddictApplicationsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/applications/{id}';
};

export type DeleteApiOpeniddictApplicationsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiOpeniddictApplicationsByIdError = DeleteApiOpeniddictApplicationsByIdErrors[keyof DeleteApiOpeniddictApplicationsByIdErrors];

export type DeleteApiOpeniddictApplicationsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiOpeniddictApplicationsByIdResponse = DeleteApiOpeniddictApplicationsByIdResponses[keyof DeleteApiOpeniddictApplicationsByIdResponses];

export type GetApiOpeniddictApplicationsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/applications/{id}';
};

export type GetApiOpeniddictApplicationsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsByIdError = GetApiOpeniddictApplicationsByIdErrors[keyof GetApiOpeniddictApplicationsByIdErrors];

export type GetApiOpeniddictApplicationsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictApplicationDto;
};

export type GetApiOpeniddictApplicationsByIdResponse = GetApiOpeniddictApplicationsByIdResponses[keyof GetApiOpeniddictApplicationsByIdResponses];

export type PutApiOpeniddictApplicationsByIdData = {
    body?: CreateUpdateOpenIddictApplicationDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/applications/{id}';
};

export type PutApiOpeniddictApplicationsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictApplicationsByIdError = PutApiOpeniddictApplicationsByIdErrors[keyof PutApiOpeniddictApplicationsByIdErrors];

export type PutApiOpeniddictApplicationsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictApplicationDto;
};

export type PutApiOpeniddictApplicationsByIdResponse = PutApiOpeniddictApplicationsByIdResponses[keyof PutApiOpeniddictApplicationsByIdResponses];

export type PostApiOpeniddictApplicationsData = {
    body?: CreateUpdateOpenIddictApplicationDto;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications';
};

export type PostApiOpeniddictApplicationsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictApplicationsError = PostApiOpeniddictApplicationsErrors[keyof PostApiOpeniddictApplicationsErrors];

export type PostApiOpeniddictApplicationsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictApplicationDto;
};

export type PostApiOpeniddictApplicationsResponse = PostApiOpeniddictApplicationsResponses[keyof PostApiOpeniddictApplicationsResponses];

export type GetApiOpeniddictApplicationsPermissionsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications/permissions';
};

export type GetApiOpeniddictApplicationsPermissionsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsPermissionsError = GetApiOpeniddictApplicationsPermissionsErrors[keyof GetApiOpeniddictApplicationsPermissionsErrors];

export type GetApiOpeniddictApplicationsPermissionsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictApplicationsPermissionsResponse = GetApiOpeniddictApplicationsPermissionsResponses[keyof GetApiOpeniddictApplicationsPermissionsResponses];

export type GetApiOpeniddictApplicationsByIdScopesData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/applications/{id}/scopes';
};

export type GetApiOpeniddictApplicationsByIdScopesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsByIdScopesError = GetApiOpeniddictApplicationsByIdScopesErrors[keyof GetApiOpeniddictApplicationsByIdScopesErrors];

export type GetApiOpeniddictApplicationsByIdScopesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictApplicationsByIdScopesResponse = GetApiOpeniddictApplicationsByIdScopesResponses[keyof GetApiOpeniddictApplicationsByIdScopesResponses];

export type PutApiOpeniddictApplicationsByIdScopesData = {
    body?: UpdateApplicationScopesDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/applications/{id}/scopes';
};

export type PutApiOpeniddictApplicationsByIdScopesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictApplicationsByIdScopesError = PutApiOpeniddictApplicationsByIdScopesErrors[keyof PutApiOpeniddictApplicationsByIdScopesErrors];

export type PutApiOpeniddictApplicationsByIdScopesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type PutApiOpeniddictApplicationsByIdScopesResponse = PutApiOpeniddictApplicationsByIdScopesResponses[keyof PutApiOpeniddictApplicationsByIdScopesResponses];

export type GetApiOpeniddictApplicationsAvailableScopesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications/available-scopes';
};

export type GetApiOpeniddictApplicationsAvailableScopesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsAvailableScopesError = GetApiOpeniddictApplicationsAvailableScopesErrors[keyof GetApiOpeniddictApplicationsAvailableScopesErrors];

export type GetApiOpeniddictApplicationsAvailableScopesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictApplicationsAvailableScopesResponse = GetApiOpeniddictApplicationsAvailableScopesResponses[keyof GetApiOpeniddictApplicationsAvailableScopesResponses];

export type GetApiOpeniddictApplicationsClientTypesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications/client-types';
};

export type GetApiOpeniddictApplicationsClientTypesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsClientTypesError = GetApiOpeniddictApplicationsClientTypesErrors[keyof GetApiOpeniddictApplicationsClientTypesErrors];

export type GetApiOpeniddictApplicationsClientTypesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictApplicationsClientTypesResponse = GetApiOpeniddictApplicationsClientTypesResponses[keyof GetApiOpeniddictApplicationsClientTypesResponses];

export type GetApiOpeniddictApplicationsClientTypesDetailsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/applications/client-types/details';
};

export type GetApiOpeniddictApplicationsClientTypesDetailsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictApplicationsClientTypesDetailsError = GetApiOpeniddictApplicationsClientTypesDetailsErrors[keyof GetApiOpeniddictApplicationsClientTypesDetailsErrors];

export type GetApiOpeniddictApplicationsClientTypesDetailsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfObject;
};

export type GetApiOpeniddictApplicationsClientTypesDetailsResponse = GetApiOpeniddictApplicationsClientTypesDetailsResponses[keyof GetApiOpeniddictApplicationsClientTypesDetailsResponses];

export type GetApiOpeniddictAuthorizationsListData = {
    body?: never;
    path?: never;
    query?: {
        Id?: string;
        ClientId?: string;
        Subject?: string;
        Type?: string;
        Status?: string;
        CreationDate?: string;
        Scopes?: Array<string>;
    };
    url: '/api/openiddict/authorizations/list';
};

export type GetApiOpeniddictAuthorizationsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictAuthorizationsListError = GetApiOpeniddictAuthorizationsListErrors[keyof GetApiOpeniddictAuthorizationsListErrors];

export type GetApiOpeniddictAuthorizationsListResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfOpenIddictAuthorizationDto;
};

export type GetApiOpeniddictAuthorizationsListResponse = GetApiOpeniddictAuthorizationsListResponses[keyof GetApiOpeniddictAuthorizationsListResponses];

export type PostApiOpeniddictAuthorizationsListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/openiddict/authorizations/list';
};

export type PostApiOpeniddictAuthorizationsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictAuthorizationsListError = PostApiOpeniddictAuthorizationsListErrors[keyof PostApiOpeniddictAuthorizationsListErrors];

export type PostApiOpeniddictAuthorizationsListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfOpenIddictAuthorizationDto;
};

export type PostApiOpeniddictAuthorizationsListResponse = PostApiOpeniddictAuthorizationsListResponses[keyof PostApiOpeniddictAuthorizationsListResponses];

export type GetApiOpeniddictAuthorizationsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/authorizations/{id}';
};

export type GetApiOpeniddictAuthorizationsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictAuthorizationsByIdError = GetApiOpeniddictAuthorizationsByIdErrors[keyof GetApiOpeniddictAuthorizationsByIdErrors];

export type GetApiOpeniddictAuthorizationsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictAuthorizationDto;
};

export type GetApiOpeniddictAuthorizationsByIdResponse = GetApiOpeniddictAuthorizationsByIdResponses[keyof GetApiOpeniddictAuthorizationsByIdResponses];

export type GetApiOpeniddictAuthorizationsByStringIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/authorizations/by-string-id/{id}';
};

export type GetApiOpeniddictAuthorizationsByStringIdByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictAuthorizationsByStringIdByIdError = GetApiOpeniddictAuthorizationsByStringIdByIdErrors[keyof GetApiOpeniddictAuthorizationsByStringIdByIdErrors];

export type GetApiOpeniddictAuthorizationsByStringIdByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictAuthorizationDto;
};

export type GetApiOpeniddictAuthorizationsByStringIdByIdResponse = GetApiOpeniddictAuthorizationsByStringIdByIdResponses[keyof GetApiOpeniddictAuthorizationsByStringIdByIdResponses];

export type PostApiOpeniddictAuthorizationsRevokeData = {
    body?: RevokeAuthorizationDto;
    path?: never;
    query?: never;
    url: '/api/openiddict/authorizations/revoke';
};

export type PostApiOpeniddictAuthorizationsRevokeErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictAuthorizationsRevokeError = PostApiOpeniddictAuthorizationsRevokeErrors[keyof PostApiOpeniddictAuthorizationsRevokeErrors];

export type PostApiOpeniddictAuthorizationsRevokeResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type PostApiOpeniddictAuthorizationsRevokeResponse = PostApiOpeniddictAuthorizationsRevokeResponses[keyof PostApiOpeniddictAuthorizationsRevokeResponses];

export type PostApiOpeniddictAuthorizationsPruneData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/authorizations/prune';
};

export type PostApiOpeniddictAuthorizationsPruneErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictAuthorizationsPruneError = PostApiOpeniddictAuthorizationsPruneErrors[keyof PostApiOpeniddictAuthorizationsPruneErrors];

export type PostApiOpeniddictAuthorizationsPruneResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type PostApiOpeniddictAuthorizationsPruneResponse = PostApiOpeniddictAuthorizationsPruneResponses[keyof PostApiOpeniddictAuthorizationsPruneResponses];

export type GetApiOpeniddictClientPropertiesByApplicationIdData = {
    body?: never;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/client-properties/{applicationId}';
};

export type GetApiOpeniddictClientPropertiesByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictClientPropertiesByApplicationIdError = GetApiOpeniddictClientPropertiesByApplicationIdErrors[keyof GetApiOpeniddictClientPropertiesByApplicationIdErrors];

export type GetApiOpeniddictClientPropertiesByApplicationIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfOpenIddictClientPropertyDto;
};

export type GetApiOpeniddictClientPropertiesByApplicationIdResponse = GetApiOpeniddictClientPropertiesByApplicationIdResponses[keyof GetApiOpeniddictClientPropertiesByApplicationIdResponses];

export type PostApiOpeniddictClientPropertiesByApplicationIdData = {
    body?: AddOpenIddictClientPropertyDto;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/client-properties/{applicationId}';
};

export type PostApiOpeniddictClientPropertiesByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictClientPropertiesByApplicationIdError = PostApiOpeniddictClientPropertiesByApplicationIdErrors[keyof PostApiOpeniddictClientPropertiesByApplicationIdErrors];

export type PostApiOpeniddictClientPropertiesByApplicationIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictClientPropertyDto;
};

export type PostApiOpeniddictClientPropertiesByApplicationIdResponse = PostApiOpeniddictClientPropertiesByApplicationIdResponses[keyof PostApiOpeniddictClientPropertiesByApplicationIdResponses];

export type DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyData = {
    body?: never;
    path: {
        applicationId: string;
        key: string;
    };
    query?: never;
    url: '/api/openiddict/client-properties/{applicationId}/{key}';
};

export type DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyError = DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyErrors[keyof DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyErrors];

export type DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponse = DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponses[keyof DeleteApiOpeniddictClientPropertiesByApplicationIdByKeyResponses];

export type GetApiOpeniddictClientPropertiesByApplicationIdByKeyData = {
    body?: never;
    path: {
        applicationId: string;
        key: string;
    };
    query?: never;
    url: '/api/openiddict/client-properties/{applicationId}/{key}';
};

export type GetApiOpeniddictClientPropertiesByApplicationIdByKeyErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictClientPropertiesByApplicationIdByKeyError = GetApiOpeniddictClientPropertiesByApplicationIdByKeyErrors[keyof GetApiOpeniddictClientPropertiesByApplicationIdByKeyErrors];

export type GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictClientPropertyDto;
};

export type GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponse = GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponses[keyof GetApiOpeniddictClientPropertiesByApplicationIdByKeyResponses];

export type PutApiOpeniddictClientPropertiesByApplicationIdByKeyData = {
    body?: UpdateOpenIddictClientPropertyDto;
    path: {
        applicationId: string;
        key: string;
    };
    query?: never;
    url: '/api/openiddict/client-properties/{applicationId}/{key}';
};

export type PutApiOpeniddictClientPropertiesByApplicationIdByKeyErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictClientPropertiesByApplicationIdByKeyError = PutApiOpeniddictClientPropertiesByApplicationIdByKeyErrors[keyof PutApiOpeniddictClientPropertiesByApplicationIdByKeyErrors];

export type PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictClientPropertyDto;
};

export type PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponse = PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponses[keyof PutApiOpeniddictClientPropertiesByApplicationIdByKeyResponses];

export type GetApiOpeniddictClientSecretsByApplicationIdData = {
    body?: never;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/client-secrets/{applicationId}';
};

export type GetApiOpeniddictClientSecretsByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictClientSecretsByApplicationIdError = GetApiOpeniddictClientSecretsByApplicationIdErrors[keyof GetApiOpeniddictClientSecretsByApplicationIdErrors];

export type GetApiOpeniddictClientSecretsByApplicationIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfOpenIddictClientSecretDto;
};

export type GetApiOpeniddictClientSecretsByApplicationIdResponse = GetApiOpeniddictClientSecretsByApplicationIdResponses[keyof GetApiOpeniddictClientSecretsByApplicationIdResponses];

export type PostApiOpeniddictClientSecretsByApplicationIdData = {
    body?: CreateOpenIddictClientSecretDto;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/client-secrets/{applicationId}';
};

export type PostApiOpeniddictClientSecretsByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictClientSecretsByApplicationIdError = PostApiOpeniddictClientSecretsByApplicationIdErrors[keyof PostApiOpeniddictClientSecretsByApplicationIdErrors];

export type PostApiOpeniddictClientSecretsByApplicationIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictClientSecretDto;
};

export type PostApiOpeniddictClientSecretsByApplicationIdResponse = PostApiOpeniddictClientSecretsByApplicationIdResponses[keyof PostApiOpeniddictClientSecretsByApplicationIdResponses];

export type DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdData = {
    body?: never;
    path: {
        applicationId: string;
        secretId: string;
    };
    query?: never;
    url: '/api/openiddict/client-secrets/{applicationId}/{secretId}';
};

export type DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdError = DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdErrors[keyof DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdErrors];

export type DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponse = DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponses[keyof DeleteApiOpeniddictClientSecretsByApplicationIdBySecretIdResponses];

export type PostApiOpeniddictClientSecretsHashData = {
    body?: string;
    path?: never;
    query?: never;
    url: '/api/openiddict/client-secrets/hash';
};

export type PostApiOpeniddictClientSecretsHashErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictClientSecretsHashError = PostApiOpeniddictClientSecretsHashErrors[keyof PostApiOpeniddictClientSecretsHashErrors];

export type PostApiOpeniddictClientSecretsHashResponses = {
    /**
     * OK
     */
    200: ApiResponseOfString;
};

export type PostApiOpeniddictClientSecretsHashResponse = PostApiOpeniddictClientSecretsHashResponses[keyof PostApiOpeniddictClientSecretsHashResponses];

export type GetApiOpeniddictClientSecretsGenerateData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/client-secrets/generate';
};

export type GetApiOpeniddictClientSecretsGenerateErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictClientSecretsGenerateError = GetApiOpeniddictClientSecretsGenerateErrors[keyof GetApiOpeniddictClientSecretsGenerateErrors];

export type GetApiOpeniddictClientSecretsGenerateResponses = {
    /**
     * OK
     */
    200: ApiResponseOfString;
};

export type GetApiOpeniddictClientSecretsGenerateResponse = GetApiOpeniddictClientSecretsGenerateResponses[keyof GetApiOpeniddictClientSecretsGenerateResponses];

export type GetApiOpeniddictGrantTypesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/grant-types';
};

export type GetApiOpeniddictGrantTypesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictGrantTypesError = GetApiOpeniddictGrantTypesErrors[keyof GetApiOpeniddictGrantTypesErrors];

export type GetApiOpeniddictGrantTypesApplicationsByApplicationIdData = {
    body?: never;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/grant-types/applications/{applicationId}';
};

export type GetApiOpeniddictGrantTypesApplicationsByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictGrantTypesApplicationsByApplicationIdError = GetApiOpeniddictGrantTypesApplicationsByApplicationIdErrors[keyof GetApiOpeniddictGrantTypesApplicationsByApplicationIdErrors];

export type PutApiOpeniddictGrantTypesApplicationsByApplicationIdData = {
    body?: IdentityUpdateApplicationGrantTypesDto;
    path: {
        applicationId: string;
    };
    query?: never;
    url: '/api/openiddict/grant-types/applications/{applicationId}';
};

export type PutApiOpeniddictGrantTypesApplicationsByApplicationIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictGrantTypesApplicationsByApplicationIdError = PutApiOpeniddictGrantTypesApplicationsByApplicationIdErrors[keyof PutApiOpeniddictGrantTypesApplicationsByApplicationIdErrors];

export type GetApiOpeniddictGrantTypesValidateCombinationData = {
    body?: never;
    path?: never;
    query?: {
        grantTypes?: Array<string>;
    };
    url: '/api/openiddict/grant-types/validate-combination';
};

export type GetApiOpeniddictGrantTypesValidateCombinationErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictGrantTypesValidateCombinationError = GetApiOpeniddictGrantTypesValidateCombinationErrors[keyof GetApiOpeniddictGrantTypesValidateCombinationErrors];

export type GetApiOpeniddictGrantTypesRecommendedSettingsData = {
    body?: never;
    path?: never;
    query?: {
        grantTypes?: Array<string>;
    };
    url: '/api/openiddict/grant-types/recommended-settings';
};

export type GetApiOpeniddictGrantTypesRecommendedSettingsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictGrantTypesRecommendedSettingsError = GetApiOpeniddictGrantTypesRecommendedSettingsErrors[keyof GetApiOpeniddictGrantTypesRecommendedSettingsErrors];

export type GetApiOpeniddictRequirementsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/requirements';
};

export type GetApiOpeniddictRequirementsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictRequirementsError = GetApiOpeniddictRequirementsErrors[keyof GetApiOpeniddictRequirementsErrors];

export type GetApiOpeniddictRequirementsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfObject;
};

export type GetApiOpeniddictRequirementsResponse = GetApiOpeniddictRequirementsResponses[keyof GetApiOpeniddictRequirementsResponses];

export type PostApiOpeniddictResourcesListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/openiddict/resources/list';
};

export type PostApiOpeniddictResourcesListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictResourcesListError = PostApiOpeniddictResourcesListErrors[keyof PostApiOpeniddictResourcesListErrors];

export type PostApiOpeniddictResourcesListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfOpenIddictResourceDto;
};

export type PostApiOpeniddictResourcesListResponse = PostApiOpeniddictResourcesListResponses[keyof PostApiOpeniddictResourcesListResponses];

export type DeleteApiOpeniddictResourcesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/resources/{id}';
};

export type DeleteApiOpeniddictResourcesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiOpeniddictResourcesByIdError = DeleteApiOpeniddictResourcesByIdErrors[keyof DeleteApiOpeniddictResourcesByIdErrors];

export type DeleteApiOpeniddictResourcesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiOpeniddictResourcesByIdResponse = DeleteApiOpeniddictResourcesByIdResponses[keyof DeleteApiOpeniddictResourcesByIdResponses];

export type GetApiOpeniddictResourcesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/resources/{id}';
};

export type GetApiOpeniddictResourcesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictResourcesByIdError = GetApiOpeniddictResourcesByIdErrors[keyof GetApiOpeniddictResourcesByIdErrors];

export type GetApiOpeniddictResourcesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictResourceDto;
};

export type GetApiOpeniddictResourcesByIdResponse = GetApiOpeniddictResourcesByIdResponses[keyof GetApiOpeniddictResourcesByIdResponses];

export type PutApiOpeniddictResourcesByIdData = {
    body?: CreateUpdateOpenIddictResourceDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/resources/{id}';
};

export type PutApiOpeniddictResourcesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictResourcesByIdError = PutApiOpeniddictResourcesByIdErrors[keyof PutApiOpeniddictResourcesByIdErrors];

export type PutApiOpeniddictResourcesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictResourceDto;
};

export type PutApiOpeniddictResourcesByIdResponse = PutApiOpeniddictResourcesByIdResponses[keyof PutApiOpeniddictResourcesByIdResponses];

export type PostApiOpeniddictResourcesData = {
    body?: CreateUpdateOpenIddictResourceDto;
    path?: never;
    query?: never;
    url: '/api/openiddict/resources';
};

export type PostApiOpeniddictResourcesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictResourcesError = PostApiOpeniddictResourcesErrors[keyof PostApiOpeniddictResourcesErrors];

export type PostApiOpeniddictResourcesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictResourceDto;
};

export type PostApiOpeniddictResourcesResponse = PostApiOpeniddictResourcesResponses[keyof PostApiOpeniddictResourcesResponses];

export type GetApiOpeniddictResourcesAvailableResourcesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/resources/available-resources';
};

export type GetApiOpeniddictResourcesAvailableResourcesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictResourcesAvailableResourcesError = GetApiOpeniddictResourcesAvailableResourcesErrors[keyof GetApiOpeniddictResourcesAvailableResourcesErrors];

export type GetApiOpeniddictResourcesAvailableResourcesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfLabelValueDto1;
};

export type GetApiOpeniddictResourcesAvailableResourcesResponse = GetApiOpeniddictResourcesAvailableResourcesResponses[keyof GetApiOpeniddictResourcesAvailableResourcesResponses];

export type PostApiOpeniddictScopesListData = {
    body?: QueryParameters;
    path?: never;
    query?: never;
    url: '/api/openiddict/scopes/list';
};

export type PostApiOpeniddictScopesListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictScopesListError = PostApiOpeniddictScopesListErrors[keyof PostApiOpeniddictScopesListErrors];

export type PostApiOpeniddictScopesListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfOpenIddictScopeDto;
};

export type PostApiOpeniddictScopesListResponse = PostApiOpeniddictScopesListResponses[keyof PostApiOpeniddictScopesListResponses];

export type DeleteApiOpeniddictScopesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/scopes/{id}';
};

export type DeleteApiOpeniddictScopesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiOpeniddictScopesByIdError = DeleteApiOpeniddictScopesByIdErrors[keyof DeleteApiOpeniddictScopesByIdErrors];

export type DeleteApiOpeniddictScopesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiOpeniddictScopesByIdResponse = DeleteApiOpeniddictScopesByIdResponses[keyof DeleteApiOpeniddictScopesByIdResponses];

export type GetApiOpeniddictScopesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/scopes/{id}';
};

export type GetApiOpeniddictScopesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictScopesByIdError = GetApiOpeniddictScopesByIdErrors[keyof GetApiOpeniddictScopesByIdErrors];

export type GetApiOpeniddictScopesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictScopeDto;
};

export type GetApiOpeniddictScopesByIdResponse = GetApiOpeniddictScopesByIdResponses[keyof GetApiOpeniddictScopesByIdResponses];

export type PutApiOpeniddictScopesByIdData = {
    body?: CreateUpdateOpenIddictScopeDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/scopes/{id}';
};

export type PutApiOpeniddictScopesByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictScopesByIdError = PutApiOpeniddictScopesByIdErrors[keyof PutApiOpeniddictScopesByIdErrors];

export type PutApiOpeniddictScopesByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictScopeDto;
};

export type PutApiOpeniddictScopesByIdResponse = PutApiOpeniddictScopesByIdResponses[keyof PutApiOpeniddictScopesByIdResponses];

export type PostApiOpeniddictScopesData = {
    body?: CreateUpdateOpenIddictScopeDto;
    path?: never;
    query?: never;
    url: '/api/openiddict/scopes';
};

export type PostApiOpeniddictScopesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictScopesError = PostApiOpeniddictScopesErrors[keyof PostApiOpeniddictScopesErrors];

export type PostApiOpeniddictScopesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictScopeDto;
};

export type PostApiOpeniddictScopesResponse = PostApiOpeniddictScopesResponses[keyof PostApiOpeniddictScopesResponses];

export type GetApiOpeniddictScopesAvailableResourcesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/scopes/available-resources';
};

export type GetApiOpeniddictScopesAvailableResourcesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictScopesAvailableResourcesError = GetApiOpeniddictScopesAvailableResourcesErrors[keyof GetApiOpeniddictScopesAvailableResourcesErrors];

export type GetApiOpeniddictScopesAvailableResourcesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictScopesAvailableResourcesResponse = GetApiOpeniddictScopesAvailableResourcesResponses[keyof GetApiOpeniddictScopesAvailableResourcesResponses];

export type GetApiOpeniddictScopesByIdResourcesData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/scopes/{id}/resources';
};

export type GetApiOpeniddictScopesByIdResourcesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictScopesByIdResourcesError = GetApiOpeniddictScopesByIdResourcesErrors[keyof GetApiOpeniddictScopesByIdResourcesErrors];

export type GetApiOpeniddictScopesByIdResourcesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type GetApiOpeniddictScopesByIdResourcesResponse = GetApiOpeniddictScopesByIdResourcesResponses[keyof GetApiOpeniddictScopesByIdResourcesResponses];

export type PutApiOpeniddictScopesByIdResourcesData = {
    body?: UpdateScopeResourcesDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/scopes/{id}/resources';
};

export type PutApiOpeniddictScopesByIdResourcesErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiOpeniddictScopesByIdResourcesError = PutApiOpeniddictScopesByIdResourcesErrors[keyof PutApiOpeniddictScopesByIdResourcesErrors];

export type PutApiOpeniddictScopesByIdResourcesResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfString;
};

export type PutApiOpeniddictScopesByIdResourcesResponse = PutApiOpeniddictScopesByIdResourcesResponses[keyof PutApiOpeniddictScopesByIdResourcesResponses];

export type GetApiOpeniddictTokensData = {
    body?: never;
    path?: never;
    query?: {
        Subject?: string;
        ClientId?: string;
        Status?: string;
        TokenType?: string;
    };
    url: '/api/openiddict/tokens';
};

export type GetApiOpeniddictTokensErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictTokensError = GetApiOpeniddictTokensErrors[keyof GetApiOpeniddictTokensErrors];

export type GetApiOpeniddictTokensResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfOpenIddictTokenDto;
};

export type GetApiOpeniddictTokensResponse = GetApiOpeniddictTokensResponses[keyof GetApiOpeniddictTokensResponses];

export type GetApiOpeniddictTokensByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/openiddict/tokens/{id}';
};

export type GetApiOpeniddictTokensByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiOpeniddictTokensByIdError = GetApiOpeniddictTokensByIdErrors[keyof GetApiOpeniddictTokensByIdErrors];

export type GetApiOpeniddictTokensByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfOpenIddictTokenDto;
};

export type GetApiOpeniddictTokensByIdResponse = GetApiOpeniddictTokensByIdResponses[keyof GetApiOpeniddictTokensByIdResponses];

export type PostApiOpeniddictTokensRevokeData = {
    body?: RevokeTokenDto;
    path?: never;
    query?: never;
    url: '/api/openiddict/tokens/revoke';
};

export type PostApiOpeniddictTokensRevokeErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictTokensRevokeError = PostApiOpeniddictTokensRevokeErrors[keyof PostApiOpeniddictTokensRevokeErrors];

export type PostApiOpeniddictTokensRevokeResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type PostApiOpeniddictTokensRevokeResponse = PostApiOpeniddictTokensRevokeResponses[keyof PostApiOpeniddictTokensRevokeResponses];

export type PostApiOpeniddictTokensPruneData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/openiddict/tokens/prune';
};

export type PostApiOpeniddictTokensPruneErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiOpeniddictTokensPruneError = PostApiOpeniddictTokensPruneErrors[keyof PostApiOpeniddictTokensPruneErrors];

export type PostApiOpeniddictTokensPruneResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type PostApiOpeniddictTokensPruneResponse = PostApiOpeniddictTokensPruneResponses[keyof PostApiOpeniddictTokensPruneResponses];

export type GetApiPermissionCheckData = {
    body?: never;
    path?: never;
    query?: {
        name?: string;
    };
    url: '/api/permission-check';
};

export type GetApiPermissionCheckResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiPermissionCheckMultipleData = {
    body?: never;
    path?: never;
    query?: {
        names?: Array<string>;
    };
    url: '/api/permission-check/multiple';
};

export type GetApiPermissionCheckMultipleResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiPermissionCheckUserData = {
    body?: never;
    path?: never;
    query?: {
        userId?: string;
        name?: string;
    };
    url: '/api/permission-check/user';
};

export type GetApiPermissionCheckUserResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiPermissionManagementPermissionsData = {
    body?: never;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/permission-management/permissions';
};

export type GetApiPermissionManagementPermissionsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiPermissionManagementPermissionsError = GetApiPermissionManagementPermissionsErrors[keyof GetApiPermissionManagementPermissionsErrors];

export type GetApiPermissionManagementPermissionsResponses = {
    /**
     * OK
     */
    200: GetPermissionListResultDto;
};

export type GetApiPermissionManagementPermissionsResponse = GetApiPermissionManagementPermissionsResponses[keyof GetApiPermissionManagementPermissionsResponses];

export type PutApiPermissionManagementPermissionsData = {
    body?: UpdatePermissionsDto;
    path?: never;
    query?: {
        providerName?: string;
        providerKey?: string;
    };
    url: '/api/permission-management/permissions';
};

export type PutApiPermissionManagementPermissionsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiPermissionManagementPermissionsError = PutApiPermissionManagementPermissionsErrors[keyof PutApiPermissionManagementPermissionsErrors];

export type PutApiPermissionManagementPermissionsResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiAccountMyProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type GetApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError = GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type GetApiAccountMyProfileResponse = GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
    body?: UpdateProfileDto;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type PutApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError = PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type PutApiAccountMyProfileResponse = PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
    body?: ChangePasswordInput;
    path?: never;
    query?: never;
    url: '/api/account/my-profile/change-password';
};

export type PostApiAccountMyProfileChangePasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError = PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityRolesAllData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/identity/roles/all';
};

export type GetApiIdentityRolesAllErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityRolesAllError = GetApiIdentityRolesAllErrors[keyof GetApiIdentityRolesAllErrors];

export type GetApiIdentityRolesAllResponses = {
    /**
     * OK
     */
    200: ListResultDtoOfIdentityRoleDto;
};

export type GetApiIdentityRolesAllResponse = GetApiIdentityRolesAllResponses[keyof GetApiIdentityRolesAllResponses];

export type GetApiIdentityRolesData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/roles';
};

export type GetApiIdentityRolesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityRolesError = GetApiIdentityRolesErrors[keyof GetApiIdentityRolesErrors];

export type GetApiIdentityRolesResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfIdentityRoleDto;
};

export type GetApiIdentityRolesResponse = GetApiIdentityRolesResponses[keyof GetApiIdentityRolesResponses];

export type PostApiIdentityRolesData = {
    body?: IdentityRoleCreateDto;
    path?: never;
    query?: never;
    url: '/api/identity/roles';
};

export type PostApiIdentityRolesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdentityRolesError = PostApiIdentityRolesErrors[keyof PostApiIdentityRolesErrors];

export type PostApiIdentityRolesResponses = {
    /**
     * OK
     */
    200: IdentityRoleDto;
};

export type PostApiIdentityRolesResponse = PostApiIdentityRolesResponses[keyof PostApiIdentityRolesResponses];

export type DeleteApiIdentityRolesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type DeleteApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdentityRolesByIdError = DeleteApiIdentityRolesByIdErrors[keyof DeleteApiIdentityRolesByIdErrors];

export type DeleteApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityRolesByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type GetApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityRolesByIdError = GetApiIdentityRolesByIdErrors[keyof GetApiIdentityRolesByIdErrors];

export type GetApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: IdentityRoleDto;
};

export type GetApiIdentityRolesByIdResponse = GetApiIdentityRolesByIdResponses[keyof GetApiIdentityRolesByIdResponses];

export type PutApiIdentityRolesByIdData = {
    body?: IdentityRoleUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{id}';
};

export type PutApiIdentityRolesByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdentityRolesByIdError = PutApiIdentityRolesByIdErrors[keyof PutApiIdentityRolesByIdErrors];

export type PutApiIdentityRolesByIdResponses = {
    /**
     * OK
     */
    200: IdentityRoleDto;
};

export type PutApiIdentityRolesByIdResponse = PutApiIdentityRolesByIdResponses[keyof PutApiIdentityRolesByIdResponses];

export type GetApiIdentityRolesByRoleIdClaimsData = {
    body?: never;
    path: {
        roleId: string;
    };
    query?: never;
    url: '/api/identity/roles/{roleId}/claims';
};

export type GetApiIdentityRolesByRoleIdClaimsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityRolesByRoleIdClaimsError = GetApiIdentityRolesByRoleIdClaimsErrors[keyof GetApiIdentityRolesByRoleIdClaimsErrors];

export type GetApiIdentityRolesByRoleIdClaimsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfRoleClaimDto;
};

export type GetApiIdentityRolesByRoleIdClaimsResponse = GetApiIdentityRolesByRoleIdClaimsResponses[keyof GetApiIdentityRolesByRoleIdClaimsResponses];

export type PostApiIdentityRolesByRoleIdClaimsData = {
    body?: never;
    path: {
        roleId: string;
    };
    query?: {
        ClaimType?: string;
        ClaimValue?: string;
    };
    url: '/api/identity/roles/{roleId}/claims';
};

export type PostApiIdentityRolesByRoleIdClaimsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityRolesByRoleIdClaimsError = PostApiIdentityRolesByRoleIdClaimsErrors[keyof PostApiIdentityRolesByRoleIdClaimsErrors];

export type PostApiIdentityRolesByRoleIdClaimsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfRoleClaimDto;
};

export type PostApiIdentityRolesByRoleIdClaimsResponse = PostApiIdentityRolesByRoleIdClaimsResponses[keyof PostApiIdentityRolesByRoleIdClaimsResponses];

export type DeleteApiIdentityRolesByRoleIdClaimsByIdData = {
    body?: never;
    path: {
        roleId: string;
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{roleId}/claims/{id}';
};

export type DeleteApiIdentityRolesByRoleIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiIdentityRolesByRoleIdClaimsByIdError = DeleteApiIdentityRolesByRoleIdClaimsByIdErrors[keyof DeleteApiIdentityRolesByRoleIdClaimsByIdErrors];

export type DeleteApiIdentityRolesByRoleIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiIdentityRolesByRoleIdClaimsByIdResponse = DeleteApiIdentityRolesByRoleIdClaimsByIdResponses[keyof DeleteApiIdentityRolesByRoleIdClaimsByIdResponses];

export type GetApiIdentityRolesByRoleIdClaimsByIdData = {
    body?: never;
    path: {
        roleId: string;
        id: string;
    };
    query?: never;
    url: '/api/identity/roles/{roleId}/claims/{id}';
};

export type GetApiIdentityRolesByRoleIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityRolesByRoleIdClaimsByIdError = GetApiIdentityRolesByRoleIdClaimsByIdErrors[keyof GetApiIdentityRolesByRoleIdClaimsByIdErrors];

export type GetApiIdentityRolesByRoleIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfRoleClaimDto;
};

export type GetApiIdentityRolesByRoleIdClaimsByIdResponse = GetApiIdentityRolesByRoleIdClaimsByIdResponses[keyof GetApiIdentityRolesByRoleIdClaimsByIdResponses];

export type PutApiIdentityRolesByRoleIdClaimsByIdData = {
    body?: never;
    path: {
        roleId: string;
        id: string;
    };
    query?: {
        ClaimType?: string;
        ClaimValue?: string;
    };
    url: '/api/identity/roles/{roleId}/claims/{id}';
};

export type PutApiIdentityRolesByRoleIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiIdentityRolesByRoleIdClaimsByIdError = PutApiIdentityRolesByRoleIdClaimsByIdErrors[keyof PutApiIdentityRolesByRoleIdClaimsByIdErrors];

export type PutApiIdentityRolesByRoleIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfRoleClaimDto;
};

export type PutApiIdentityRolesByRoleIdClaimsByIdResponse = PutApiIdentityRolesByRoleIdClaimsByIdResponses[keyof PutApiIdentityRolesByRoleIdClaimsByIdResponses];

export type PostApiSecurityLogsListData = {
    body?: GetSecurityLogsInput;
    path?: never;
    query?: never;
    url: '/api/security-logs/list';
};

export type PostApiSecurityLogsListErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiSecurityLogsListError = PostApiSecurityLogsListErrors[keyof PostApiSecurityLogsListErrors];

export type PostApiSecurityLogsListResponses = {
    /**
     * OK
     */
    200: PagedApiResponseOfPagedResultDtoOfSecurityLogDto;
};

export type PostApiSecurityLogsListResponse = PostApiSecurityLogsListResponses[keyof PostApiSecurityLogsListResponses];

export type GetApiSecurityLogsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/security-logs/{id}';
};

export type GetApiSecurityLogsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiSecurityLogsByIdError = GetApiSecurityLogsByIdErrors[keyof GetApiSecurityLogsByIdErrors];

export type GetApiSecurityLogsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfSecurityLogDto;
};

export type GetApiSecurityLogsByIdResponse = GetApiSecurityLogsByIdResponses[keyof GetApiSecurityLogsByIdResponses];

export type PostApiSecurityLogsMyLogsData = {
    body?: GetSecurityLogsInput;
    path?: never;
    query?: never;
    url: '/api/security-logs/my-logs';
};

export type PostApiSecurityLogsMyLogsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiSecurityLogsMyLogsError = PostApiSecurityLogsMyLogsErrors[keyof PostApiSecurityLogsMyLogsErrors];

export type PostApiSecurityLogsMyLogsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfPagedResultDtoOfSecurityLogDto;
};

export type PostApiSecurityLogsMyLogsResponse = PostApiSecurityLogsMyLogsResponses[keyof PostApiSecurityLogsMyLogsResponses];

export type DeleteApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError = DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type GetApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError = GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type GetApiMultiTenancyTenantsByIdResponse = GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
    body?: TenantUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type PutApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError = PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PutApiMultiTenancyTenantsByIdResponse = PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/multi-tenancy/tenants';
};

export type GetApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError = GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTenantDto;
};

export type GetApiMultiTenancyTenantsResponse = GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
    body?: TenantCreateDto;
    path?: never;
    query?: never;
    url: '/api/multi-tenancy/tenants';
};

export type PostApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError = PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PostApiMultiTenancyTenantsResponse = PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError = DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError = GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse = GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        defaultConnectionString?: string;
    };
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError = PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementTimezoneData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/timezone';
};

export type GetApiSettingManagementTimezoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiSettingManagementTimezoneError = GetApiSettingManagementTimezoneErrors[keyof GetApiSettingManagementTimezoneErrors];

export type GetApiSettingManagementTimezoneResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiSettingManagementTimezoneResponse = GetApiSettingManagementTimezoneResponses[keyof GetApiSettingManagementTimezoneResponses];

export type PostApiSettingManagementTimezoneData = {
    body?: never;
    path?: never;
    query?: {
        timezone?: string;
    };
    url: '/api/setting-management/timezone';
};

export type PostApiSettingManagementTimezoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiSettingManagementTimezoneError = PostApiSettingManagementTimezoneErrors[keyof PostApiSettingManagementTimezoneErrors];

export type PostApiSettingManagementTimezoneResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiSettingManagementTimezoneTimezonesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/setting-management/timezone/timezones';
};

export type GetApiSettingManagementTimezoneTimezonesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiSettingManagementTimezoneTimezonesError = GetApiSettingManagementTimezoneTimezonesErrors[keyof GetApiSettingManagementTimezoneTimezonesErrors];

export type GetApiSettingManagementTimezoneTimezonesResponses = {
    /**
     * OK
     */
    200: Array<NameValue>;
};

export type GetApiSettingManagementTimezoneTimezonesResponse = GetApiSettingManagementTimezoneTimezonesResponses[keyof GetApiSettingManagementTimezoneTimezonesResponses];

export type DeleteApiIdentityUsersByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type DeleteApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiIdentityUsersByIdError = DeleteApiIdentityUsersByIdErrors[keyof DeleteApiIdentityUsersByIdErrors];

export type DeleteApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityUsersByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type GetApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByIdError = GetApiIdentityUsersByIdErrors[keyof GetApiIdentityUsersByIdErrors];

export type GetApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type GetApiIdentityUsersByIdResponse = GetApiIdentityUsersByIdResponses[keyof GetApiIdentityUsersByIdResponses];

export type PutApiIdentityUsersByIdData = {
    body?: IdentityUserUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}';
};

export type PutApiIdentityUsersByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdentityUsersByIdError = PutApiIdentityUsersByIdErrors[keyof PutApiIdentityUsersByIdErrors];

export type PutApiIdentityUsersByIdResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PutApiIdentityUsersByIdResponse = PutApiIdentityUsersByIdResponses[keyof PutApiIdentityUsersByIdResponses];

export type GetApiIdentityUsersData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/users';
};

export type GetApiIdentityUsersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersError = GetApiIdentityUsersErrors[keyof GetApiIdentityUsersErrors];

export type GetApiIdentityUsersResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfIdentityUserDto;
};

export type GetApiIdentityUsersResponse = GetApiIdentityUsersResponses[keyof GetApiIdentityUsersResponses];

export type PostApiIdentityUsersData = {
    body?: IdentityUserCreateDto;
    path?: never;
    query?: never;
    url: '/api/identity/users';
};

export type PostApiIdentityUsersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdentityUsersError = PostApiIdentityUsersErrors[keyof PostApiIdentityUsersErrors];

export type PostApiIdentityUsersResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PostApiIdentityUsersResponse = PostApiIdentityUsersResponses[keyof PostApiIdentityUsersResponses];

export type GetApiIdentityUsersByIdRolesData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}/roles';
};

export type GetApiIdentityUsersByIdRolesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByIdRolesError = GetApiIdentityUsersByIdRolesErrors[keyof GetApiIdentityUsersByIdRolesErrors];

export type GetApiIdentityUsersByIdRolesResponses = {
    /**
     * OK
     */
    200: ListResultDtoOfIdentityRoleDto;
};

export type GetApiIdentityUsersByIdRolesResponse = GetApiIdentityUsersByIdRolesResponses[keyof GetApiIdentityUsersByIdRolesResponses];

export type PutApiIdentityUsersByIdRolesData = {
    body?: IdentityUserUpdateRolesDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{id}/roles';
};

export type PutApiIdentityUsersByIdRolesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiIdentityUsersByIdRolesError = PutApiIdentityUsersByIdRolesErrors[keyof PutApiIdentityUsersByIdRolesErrors];

export type PutApiIdentityUsersByIdRolesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityUsersAssignableRolesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/identity/users/assignable-roles';
};

export type GetApiIdentityUsersAssignableRolesErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersAssignableRolesError = GetApiIdentityUsersAssignableRolesErrors[keyof GetApiIdentityUsersAssignableRolesErrors];

export type GetApiIdentityUsersAssignableRolesResponses = {
    /**
     * OK
     */
    200: ListResultDtoOfIdentityRoleDto;
};

export type GetApiIdentityUsersAssignableRolesResponse = GetApiIdentityUsersAssignableRolesResponses[keyof GetApiIdentityUsersAssignableRolesResponses];

export type GetApiIdentityUsersByUsernameByUserNameData = {
    body?: never;
    path: {
        userName: string;
    };
    query?: never;
    url: '/api/identity/users/by-username/{userName}';
};

export type GetApiIdentityUsersByUsernameByUserNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByUsernameByUserNameError = GetApiIdentityUsersByUsernameByUserNameErrors[keyof GetApiIdentityUsersByUsernameByUserNameErrors];

export type GetApiIdentityUsersByUsernameByUserNameResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type GetApiIdentityUsersByUsernameByUserNameResponse = GetApiIdentityUsersByUsernameByUserNameResponses[keyof GetApiIdentityUsersByUsernameByUserNameResponses];

export type GetApiIdentityUsersByEmailByEmailData = {
    body?: never;
    path: {
        email: string;
    };
    query?: never;
    url: '/api/identity/users/by-email/{email}';
};

export type GetApiIdentityUsersByEmailByEmailErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersByEmailByEmailError = GetApiIdentityUsersByEmailByEmailErrors[keyof GetApiIdentityUsersByEmailByEmailErrors];

export type GetApiIdentityUsersByEmailByEmailResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type GetApiIdentityUsersByEmailByEmailResponse = GetApiIdentityUsersByEmailByEmailResponses[keyof GetApiIdentityUsersByEmailByEmailResponses];

export type GetApiIdentityUsersByUserIdClaimsData = {
    body?: never;
    path: {
        userId: string;
    };
    query?: never;
    url: '/api/identity/users/{userId}/claims';
};

export type GetApiIdentityUsersByUserIdClaimsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityUsersByUserIdClaimsError = GetApiIdentityUsersByUserIdClaimsErrors[keyof GetApiIdentityUsersByUserIdClaimsErrors];

export type GetApiIdentityUsersByUserIdClaimsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfListOfUserClaimDto;
};

export type GetApiIdentityUsersByUserIdClaimsResponse = GetApiIdentityUsersByUserIdClaimsResponses[keyof GetApiIdentityUsersByUserIdClaimsResponses];

export type PostApiIdentityUsersByUserIdClaimsData = {
    body?: never;
    path: {
        userId: string;
    };
    query: {
        ClaimType: string;
        ClaimValue: string;
    };
    url: '/api/identity/users/{userId}/claims';
};

export type PostApiIdentityUsersByUserIdClaimsErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PostApiIdentityUsersByUserIdClaimsError = PostApiIdentityUsersByUserIdClaimsErrors[keyof PostApiIdentityUsersByUserIdClaimsErrors];

export type PostApiIdentityUsersByUserIdClaimsResponses = {
    /**
     * OK
     */
    200: ApiResponseOfUserClaimDto;
};

export type PostApiIdentityUsersByUserIdClaimsResponse = PostApiIdentityUsersByUserIdClaimsResponses[keyof PostApiIdentityUsersByUserIdClaimsResponses];

export type DeleteApiIdentityUsersByUserIdClaimsByIdData = {
    body?: never;
    path: {
        userId: string;
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{userId}/claims/{id}';
};

export type DeleteApiIdentityUsersByUserIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type DeleteApiIdentityUsersByUserIdClaimsByIdError = DeleteApiIdentityUsersByUserIdClaimsByIdErrors[keyof DeleteApiIdentityUsersByUserIdClaimsByIdErrors];

export type DeleteApiIdentityUsersByUserIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfBoolean;
};

export type DeleteApiIdentityUsersByUserIdClaimsByIdResponse = DeleteApiIdentityUsersByUserIdClaimsByIdResponses[keyof DeleteApiIdentityUsersByUserIdClaimsByIdResponses];

export type GetApiIdentityUsersByUserIdClaimsByIdData = {
    body?: never;
    path: {
        userId: string;
        id: string;
    };
    query?: never;
    url: '/api/identity/users/{userId}/claims/{id}';
};

export type GetApiIdentityUsersByUserIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type GetApiIdentityUsersByUserIdClaimsByIdError = GetApiIdentityUsersByUserIdClaimsByIdErrors[keyof GetApiIdentityUsersByUserIdClaimsByIdErrors];

export type GetApiIdentityUsersByUserIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfUserClaimDto;
};

export type GetApiIdentityUsersByUserIdClaimsByIdResponse = GetApiIdentityUsersByUserIdClaimsByIdResponses[keyof GetApiIdentityUsersByUserIdClaimsByIdResponses];

export type PutApiIdentityUsersByUserIdClaimsByIdData = {
    body?: never;
    path: {
        userId: string;
        id: string;
    };
    query: {
        ClaimType: string;
        ClaimValue: string;
    };
    url: '/api/identity/users/{userId}/claims/{id}';
};

export type PutApiIdentityUsersByUserIdClaimsByIdErrors = {
    /**
     * Bad Request
     */
    400: ProblemDetails;
    /**
     * Unauthorized
     */
    401: ProblemDetails;
    /**
     * Forbidden
     */
    403: ProblemDetails;
    /**
     * Not Found
     */
    404: ProblemDetails;
    /**
     * Internal Server Error
     */
    500: ProblemDetails;
    /**
     * Not Implemented
     */
    501: ProblemDetails;
};

export type PutApiIdentityUsersByUserIdClaimsByIdError = PutApiIdentityUsersByUserIdClaimsByIdErrors[keyof PutApiIdentityUsersByUserIdClaimsByIdErrors];

export type PutApiIdentityUsersByUserIdClaimsByIdResponses = {
    /**
     * OK
     */
    200: ApiResponseOfUserClaimDto;
};

export type PutApiIdentityUsersByUserIdClaimsByIdResponse = PutApiIdentityUsersByUserIdClaimsByIdResponses[keyof PutApiIdentityUsersByUserIdClaimsByIdResponses];

export type GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdData = {
    body?: never;
    path: {
        userId: string;
    };
    query?: never;
    url: '/api/idp/user-concurrent-login/concurrent-login-prevention-mode/{userId}';
};

export type GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdError = GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdErrors[keyof GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdErrors];

export type GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponses = {
    /**
     * OK
     */
    200: ConcurrentLoginPreventionDto;
};

export type GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponse = GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponses[keyof GetApiIdpUserConcurrentLoginConcurrentLoginPreventionModeByUserIdResponses];

export type PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeData = {
    body?: ConcurrentLoginPreventionDto;
    path?: never;
    query?: never;
    url: '/api/idp/user-concurrent-login/set-concurrent-login-prevention-mode';
};

export type PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeError = PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeErrors[keyof PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeErrors];

export type PostApiIdpUserConcurrentLoginSetConcurrentLoginPreventionModeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiIdentityUsersLookupByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/identity/users/lookup/{id}';
};

export type GetApiIdentityUsersLookupByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupByIdError = GetApiIdentityUsersLookupByIdErrors[keyof GetApiIdentityUsersLookupByIdErrors];

export type GetApiIdentityUsersLookupByIdResponses = {
    /**
     * OK
     */
    200: UserData;
};

export type GetApiIdentityUsersLookupByIdResponse = GetApiIdentityUsersLookupByIdResponses[keyof GetApiIdentityUsersLookupByIdResponses];

export type GetApiIdentityUsersLookupByUsernameByUserNameData = {
    body?: never;
    path: {
        userName: string;
    };
    query?: never;
    url: '/api/identity/users/lookup/by-username/{userName}';
};

export type GetApiIdentityUsersLookupByUsernameByUserNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupByUsernameByUserNameError = GetApiIdentityUsersLookupByUsernameByUserNameErrors[keyof GetApiIdentityUsersLookupByUsernameByUserNameErrors];

export type GetApiIdentityUsersLookupByUsernameByUserNameResponses = {
    /**
     * OK
     */
    200: UserData;
};

export type GetApiIdentityUsersLookupByUsernameByUserNameResponse = GetApiIdentityUsersLookupByUsernameByUserNameResponses[keyof GetApiIdentityUsersLookupByUsernameByUserNameResponses];

export type GetApiIdentityUsersLookupSearchData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
        ExtraProperties?: {
            [key: string]: unknown;
        };
    };
    url: '/api/identity/users/lookup/search';
};

export type GetApiIdentityUsersLookupSearchErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupSearchError = GetApiIdentityUsersLookupSearchErrors[keyof GetApiIdentityUsersLookupSearchErrors];

export type GetApiIdentityUsersLookupSearchResponses = {
    /**
     * OK
     */
    200: ListResultDtoOfUserData;
};

export type GetApiIdentityUsersLookupSearchResponse = GetApiIdentityUsersLookupSearchResponses[keyof GetApiIdentityUsersLookupSearchResponses];

export type GetApiIdentityUsersLookupCountData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
    };
    url: '/api/identity/users/lookup/count';
};

export type GetApiIdentityUsersLookupCountErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdentityUsersLookupCountError = GetApiIdentityUsersLookupCountErrors[keyof GetApiIdentityUsersLookupCountErrors];

export type GetApiIdentityUsersLookupCountResponses = {
    /**
     * OK
     */
    200: number;
};

export type GetApiIdentityUsersLookupCountResponse = GetApiIdentityUsersLookupCountResponses[keyof GetApiIdentityUsersLookupCountResponses];

export type GetApiIdpWindowsAuthCurrentWindowsUserData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/idp/windows-auth/current-windows-user';
};

export type GetApiIdpWindowsAuthCurrentWindowsUserErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdpWindowsAuthCurrentWindowsUserError = GetApiIdpWindowsAuthCurrentWindowsUserErrors[keyof GetApiIdpWindowsAuthCurrentWindowsUserErrors];

export type GetApiIdpWindowsAuthCurrentWindowsUserResponses = {
    /**
     * OK
     */
    200: WindowsUserResponse;
};

export type GetApiIdpWindowsAuthCurrentWindowsUserResponse = GetApiIdpWindowsAuthCurrentWindowsUserResponses[keyof GetApiIdpWindowsAuthCurrentWindowsUserResponses];

export type GetApiIdpWindowsAuthWindowsUserDetailsData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/idp/windows-auth/windows-user-details';
};

export type GetApiIdpWindowsAuthWindowsUserDetailsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiIdpWindowsAuthWindowsUserDetailsError = GetApiIdpWindowsAuthWindowsUserDetailsErrors[keyof GetApiIdpWindowsAuthWindowsUserDetailsErrors];

export type GetApiIdpWindowsAuthWindowsUserDetailsResponses = {
    /**
     * OK
     */
    200: WindowsUserDetailsResponse;
};

export type GetApiIdpWindowsAuthWindowsUserDetailsResponse = GetApiIdpWindowsAuthWindowsUserDetailsResponses[keyof GetApiIdpWindowsAuthWindowsUserDetailsResponses];

export type PostApiIdpWindowsAuthIsServiceAvailableData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/idp/windows-auth/is-service-available';
};

export type PostApiIdpWindowsAuthIsServiceAvailableErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiIdpWindowsAuthIsServiceAvailableError = PostApiIdpWindowsAuthIsServiceAvailableErrors[keyof PostApiIdpWindowsAuthIsServiceAvailableErrors];

export type PostApiIdpWindowsAuthIsServiceAvailableResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiIdpWindowsAuthIsServiceAvailableResponse = PostApiIdpWindowsAuthIsServiceAvailableResponses[keyof PostApiIdpWindowsAuthIsServiceAvailableResponses];

export type ClientOptions = {
    baseUrl: `${string}://swagger.json` | (string & {});
};