import { postApiIdentityClaimTypesList } from '@/client'
import { useQuery } from '@tanstack/react-query'
import { extractApiError } from '@/lib/query-utils'
import { QueryNames } from './QueryConstants'
import { generateExtendedQueryParameters } from '@/lib/query-utils-extended'
import { type FilterCondition } from '@/lib/interfaces/IFilterCondition'
import { toast } from '@/lib/useToast'

export const useIdentityClaimTypes = (
  pageIndex: number,
  pageSize: number,
  filterConditions: FilterCondition[] = [],
  sorting?: string  
) => {
  return useQuery({
    queryKey: [QueryNames.GetIdentityClaimTypes,  pageIndex, pageSize, JSON.stringify(filterConditions), sorting],
    queryFn: async () => {
      try {
        // Generate query parameters using the extended utility function
        const body = generateExtendedQueryParameters({
          pageIndex,
          pageSize,
          sorting,
          filterConditions,
        })

        const response = await postApiIdentityClaimTypesList({
          body
        })

        return response.data?.data
      } catch (error) {
       // Use the error extraction utility
       const { title, description } = extractApiError(error, 'Error loading clients')

       // Show toast notification
       toast({
         title,
         description,
         variant: 'destructive',
       })
        // Return empty data to prevent UI crashes
        return { items: [], totalCount: 0 }
      }
    },
  })
}
