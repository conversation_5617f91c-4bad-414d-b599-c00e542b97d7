using Imip.IdentityServer.Localization;
using Imip.IdentityServer.Permissions.Apps;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace Imip.IdentityServer.Permissions;

public class IdentityServerPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var identityProviderGroup = context.AddGroup(IdentityServerPermissions.GroupName, L("IdentityServer"));
        DefineIdentityServerPermissions(identityProviderGroup);

        // Wisma App Permissions
        var wismaGroup = context.AddGroup(WismaAppPermissions.GroupName, L("WismaApp"));
        DefineWismaPermissions(wismaGroup);

        // Ekb App Permissions
        var ekbGroup = context.AddGroup(EkbAppPermission.GroupName, L("EkbApp"));
        DefineEkbPermissions(ekbGroup);

        var jettyApprovalGroup = context.AddGroup(JettyApprovalPermission.GroupName, L("JettyApprovalApp"));
        DefineJettyApprovalPermissions(jettyApprovalGroup);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(IdentityServerPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private void DefineJettyApprovalPermissions(PermissionGroupDefinition group)
    {
        DefinePermission(group, "JettyRequest", JettyApprovalPermission.PolicyJettyRequest.Default);
        DefinePermission(group, "JettyRequestItem", JettyApprovalPermission.PolicyJettyRequestItem.Default);
        DefinePermission(group, "Attachment", JettyApprovalPermission.PolicyAttachment.Default);
        DefinePermission(group, "Report", JettyApprovalPermission.PolicyReport.Default);
        DefinePermission(group, "JettySchedule", JettyApprovalPermission.PolicyJettySchedule.Default);
        DefinePermission(group, "JettyDockedVessel", JettyApprovalPermission.PolicyJettyDockedVessel.Default);
        DefinePermission(group, "JettyManage", JettyApprovalPermission.PolicyJettyManage.Default);
        DefinePermission(group, "DocumentTemplate", JettyApprovalPermission.PolicyDocumentTemplate.Default);
        DefinePermission(group, "ApprovalRequest", JettyApprovalPermission.PolicyApprovalRequest.Default);
        DefinePermission(group, "ApprovalTemplate", JettyApprovalPermission.PolicyApprovalTemplate.Default);
        DefinePermission(group, "ApprovalApprover", JettyApprovalPermission.PolicyApprovalApprover.Default);
        DefinePermission(group, "ApprovalCriteria", JettyApprovalPermission.PolicyApprovalCriteria.Default);
        DefinePermission(group, "ApprovalStages", JettyApprovalPermission.PolicyApprovalStages.Default);
        DefinePermission(group, "ApprovalDelegation", JettyApprovalPermission.PolicyApprovalDelegation.Default);

    }

    private void DefineIdentityServerPermissions(PermissionGroupDefinition identityProviderGroup)
    {
        DefinePermission(identityProviderGroup, "OpenIddictApplicationManagement",
            IdentityServerPermissions.IdentityOpenIddictApplications.Default);
        DefinePermission(identityProviderGroup, "OpenIddictScopeManagement",
            IdentityServerPermissions.IdentityOpenIddictScopes.Default);
        DefinePermission(identityProviderGroup, "ClaimTypeManagement",
            IdentityServerPermissions.IdentityClaimTypes.Default);
        DefinePermission(identityProviderGroup, "OpenIddictResourceManagement",
            IdentityServerPermissions.IdentityOpenIddictResources.Default);
        DefinePermission(identityProviderGroup, "Claims", IdentityServerPermissions.IdentityClaims.Default);

        // Add Active Directory settings permission
        var adSettingsPermission = identityProviderGroup.AddPermission(
            IdentityServerPermissions.ActiveDirectorySettings.Default,
            L("Permission:ActiveDirectorySettings"));
        adSettingsPermission.AddChild(
            IdentityServerPermissions.ActiveDirectorySettings.Manage,
            L("Permission:ActiveDirectorySettings.Manage"));
        DefinePermission(identityProviderGroup, "SecurityLogs", IdentityServerPermissions.SecurityLogs.Default);

        DefinePermissionReadOnly(identityProviderGroup, "Import", IdentityServerPermissions.Import.Default);
    }

    private void DefineEkbPermissions(PermissionGroupDefinition group)
    {
        DefinePermission(group, "MasterAgent", EkbAppPermission.PolicyMasterAgent.Default);
        DefinePermission(group, "MasterBcType", EkbAppPermission.PolicyMasterBcType.Default);
        DefinePermission(group, "MasterBillingItem", EkbAppPermission.PolicyMasterBillingItem.Default);
        DefinePermission(group, "MasterBusinessPartner", EkbAppPermission.PolicyMasterBusinessPartner.Default);
        DefinePermission(group, "MasterCargo", EkbAppPermission.PolicyMasterCargo.Default);
        DefinePermission(group, "MasterCategoryItem", EkbAppPermission.PolicyMasterCategoryItem.Default);
        DefinePermission(group, "MasterClassification", EkbAppPermission.PolicyMasterClassification.Default);
        DefinePermission(group, "MasterCurrency", EkbAppPermission.PolicyMasterCurrency.Default);
        DefinePermission(group, "MasterDestinationPort", EkbAppPermission.PolicyMasterDestinationPort.Default);
        DefinePermission(group, "MasterJetty", EkbAppPermission.PolicyMasterJetty.Default);
        DefinePermission(group, "MasterLocalItem", EkbAppPermission.PolicyMasterLocalItem.Default);
        DefinePermission(group, "MasterPortOfLoading", EkbAppPermission.PolicyMasterPortOfLoading.Default);
        DefinePermission(group, "MasterPpjk", EkbAppPermission.PolicyMasterPpjk.Default);
        DefinePermission(group, "MasterPriceList", EkbAppPermission.PolicyMasterPriceList.Default);
        DefinePermission(group, "MasterPriceListTenant", EkbAppPermission.PolicyMasterPriceListTenant.Default);
        DefinePermission(group, "MasterSigner", EkbAppPermission.PolicyMasterSigner.Default);
        DefinePermission(group, "MasterRemark", EkbAppPermission.PolicyMasterRemark.Default);
        DefinePermission(group, "MasterTenant", EkbAppPermission.PolicyMasterTenant.Default);
        DefinePermission(group, "MasterTransType", EkbAppPermission.PolicyMasterTransType.Default);
        DefinePermission(group, "MasterSignature", EkbAppPermission.PolicyMasterSignature.Default);
        DefinePermission(group, "MasterExchangeRate", EkbAppPermission.PolicyMasterExchangeRate.Default);
        DefinePermission(group, "MasterPriceListAssistTug", EkbAppPermission.PolicyMasterPriceListAssistTug.Default);
        DefinePermission(group, "MasterPostingPeriod", EkbAppPermission.PolicyMasterPostingPeriod.Default);
        DefinePermission(group, "MasterSurveyor", EkbAppPermission.PolicyMasterSurveyor.Default);
        DefinePermission(group, "MasterTrading", EkbAppPermission.PolicyMasterTrading.Default);
        DefinePermission(group, "MasterExportClassification", EkbAppPermission.PolicyMasterExportClassification.Default);
        DefinePermission(group, "MasterUsdConversion", EkbAppPermission.PolicyMasterUsdConversion.Default);
        DefinePermission(group, "MasterTenantDocValidation", EkbAppPermission.PolicyMasterTenantDocValidation.Default);
        DefinePermission(group, "MasterWarehouse", EkbAppPermission.PolicyMasterWarehouse.Default);
        DefinePermission(group, "MasterGroupTenant", EkbAppPermission.PolicyMasterGroupTenant.Default);
        DefinePermission(group, "MasterImportTemporary", EkbAppPermission.PolicyMasterImportTemporary.Default);
        DefinePermission(group, "MasterRepresentative", EkbAppPermission.PolicyMasterRepresentative.Default);
        DefinePermission(group, "CatalogItem", EkbAppPermission.PolicyCatalogItem.Default);
        DefinePermission(group, "BoundedZoneImport", EkbAppPermission.PolicyBoundedZoneImport.Default);
        DefinePermission(group, "BoundedZoneTrading", EkbAppPermission.PolicyBoundedZoneTrading.Default);
        DefinePermission(group, "BoundedZoneFormCancel", EkbAppPermission.PolicyBoundedZoneFormCancel.Default);
        DefinePermission(group, "BoundedZoneTransaction", EkbAppPermission.PolicyBoundedZoneTransaction.Default);
        DefinePermission(group, "BoundedZoneFormBcf", EkbAppPermission.PolicyBoundedZoneFormBcf.Default);
        DefinePermission(group, "BoundedZoneErrorRecap", EkbAppPermission.PolicyBoundedZoneErrorRecap.Default);
        DefinePermission(group, "VesselExport", EkbAppPermission.PolicyVesselExport.Default);
        DefinePermission(group, "VesselImport", EkbAppPermission.PolicyVesselImport.Default);
        DefinePermission(group, "VesselLocal", EkbAppPermission.PolicyVesselLocal.Default);
        DefinePermission(group, "VesselExportTemporary", EkbAppPermission.PolicyVesselExportTemporary.Default);
        DefinePermission(group, "BillingExport", EkbAppPermission.PolicyBillingExport.Default);
        DefinePermission(group, "BillingImport", EkbAppPermission.PolicyBillingImport.Default);
        DefinePermission(group, "BillingLocal", EkbAppPermission.PolicyBillingLocal.Default);
        DefinePermission(group, "BillingQtyRevision", EkbAppPermission.PolicyBillingQtyRevision.Default);
        DefinePermission(group, "Report", EkbAppPermission.PolicyReport.Default);
        DefinePermission(group, "ReportBoundedZone", EkbAppPermission.PolicyReportBoundedZone.Default);
        DefinePermission(group, "ReportBlList", EkbAppPermission.PolicyReportBlList.Default);
        DefinePermission(group, "ReportBlListByInvoice", EkbAppPermission.PolicyReportBlListByInvoice.Default);
        DefinePermission(group, "ReportCountProcess", EkbAppPermission.PolicyReportCountProcess.Default);
        DefinePermission(group, "ReportBoundedZoneLocal", EkbAppPermission.PolicyReportBoundedZoneLocal.Default);
        DefinePermission(group, "ReportMonitoringDocument", EkbAppPermission.PolicyReportMonitoringDocument.Default);
        DefinePermission(group, "ReportSppb", EkbAppPermission.PolicyReportSppb.Default);
        DefinePermission(group, "ReportSppbByInvoice", EkbAppPermission.PolicyReportSppbByInvoice.Default);
        DefinePermission(group, "ReportSppd", EkbAppPermission.PolicyReportSppd.Default);
        DefinePermission(group, "ReportSppdByInvoice", EkbAppPermission.PolicyReportSppdByInvoice.Default);
        DefinePermission(group, "ReportVessel", EkbAppPermission.PolicyReportVessel.Default);
        DefinePermission(group, "ReportItemName", EkbAppPermission.PolicyReportItemName.Default);
        DefinePermission(group, "ReportBlListByNopen", EkbAppPermission.PolicyReportBlListByNopen.Default);
        DefinePermission(group, "ReportCargoRealization", EkbAppPermission.PolicyReportCargoRealization.Default);
        DefinePermission(group, "ReportAccounting", EkbAppPermission.PolicyReportAccounting.Default);
        DefinePermission(group, "ReportTransactionList", EkbAppPermission.PolicyReportTransactionList.Default);
        DefinePermission(group, "ReportJettyBilling", EkbAppPermission.PolicyReportJettyBilling.Default);
        DefinePermission(group, "ReportJettyBillingDetail", EkbAppPermission.PolicyReportJettyBillingDetail.Default);
        DefinePermission(group, "ReportAudit", EkbAppPermission.PolicyReportAudit.Default);
        DefinePermission(group, "ReportRecapImport", EkbAppPermission.PolicyReportRecapImport.Default);
        DefinePermission(group, "ReportSpjm", EkbAppPermission.PolicyReportSpjm.Default);
        DefinePermission(group, "ReportTally", EkbAppPermission.PolicyReportTally.Default);
        DefinePermission(group, "ReportAccountingDetail", EkbAppPermission.PolicyReportAccountingDetail.Default);
        DefinePermission(group, "ReportRecapExport", EkbAppPermission.PolicyReportRecapExport.Default);
        DefinePermission(group, "ReportReExportReImport", EkbAppPermission.PolicyReportReExportReImport.Default);
        DefinePermission(group, "ReportStockpiledAttachment", EkbAppPermission.PolicyReportStockpiledAttachment.Default);
        DefinePermission(group, "ReportRecapNopen", EkbAppPermission.PolicyReportRecapNopen.Default);
        DefinePermission(group, "ReportRecapBoundedZone", EkbAppPermission.PolicyReportRecapBoundedZone.Default);
        DefinePermission(group, "ReportMonitoringPurchasing", EkbAppPermission.PolicyReportMonitoringPurchasing.Default);
        DefinePermission(group, "ReportVesselExport", EkbAppPermission.PolicyReportVesselExport.Default);
        DefinePermission(group, "ReportJetty", EkbAppPermission.PolicyReportJetty.Default);
        DefinePermission(group, "ReportBillingSummary", EkbAppPermission.PolicyReportBillingSummary.Default);
        DefinePermission(group, "Audit", EkbAppPermission.PolicyAudit.Default);
        DefinePermission(group, "ConfigItemSAP", EkbAppPermission.PolicyConfigItemSAP.Default);
        DefinePermission(group, "ConfigDataHsCode", EkbAppPermission.PolicyConfigDataHsCode.Default);
        DefinePermission(group, "Attachment", EkbAppPermission.PolicyAttachment.Default);
        DefinePermission(group, "DocumentTemplate", EkbAppPermission.PolicyDocumentTemplate.Default);
        DefinePermission(group, "BoundedZoneTradingInvoice", EkbAppPermission.PolicyBoundedZoneTradingInvoice.Default);
        DefinePermission(group, "BoundedZoneTradingInvoiceDetail", EkbAppPermission.PolicyBoundedZoneTradingInvoiceDetail.Default);
        DefinePermission(group, "BoundedZoneImportTemporaryExport", EkbAppPermission.PolicyBoundedZoneImportTemporaryExport.Default);
        DefinePermission(group, "BoundedZoneImportNotul", EkbAppPermission.PolicyBoundedZoneImportNotul.Default);
        DefinePermission(group, "BoundedZoneImportEsign", EkbAppPermission.PolicyBoundedZoneImportEsign.Default);
        DefinePermission(group, "BoundedZoneImportInvoice", EkbAppPermission.PolicyBoundedZoneImportInvoice.Default);
        DefinePermission(group, "BoundedZoneImportInvoiceDetail", EkbAppPermission.PolicyBoundedZoneImportInvoiceDetail.Default);
    }

    private void DefineWismaPermissions(PermissionGroupDefinition group)
    {
        DefinePermission(group, "RoomType", WismaAppPermissions.PolicyRoomType.Default);
        DefinePermission(group, "RoomStatus", WismaAppPermissions.PolicyRoomStatus.Default);
        DefinePermission(group, "Room", WismaAppPermissions.PolicyRoom.Default);
        DefinePermission(group, "ReservationRoom", WismaAppPermissions.ReservationRoom.Default);
        DefinePermission(group, "Guest", WismaAppPermissions.PolicyGuest.Default);
        DefinePermission(group, "ReservationType", WismaAppPermissions.PolicyReservationType.Default);
        DefinePermission(group, "ReservationFoodAndBeverages", WismaAppPermissions.PolicyReservationFoodAndBeverages.Default);
        DefinePermission(group, "Reservation", WismaAppPermissions.PolicyReservation.Default);
        DefinePermission(group, "Payment", WismaAppPermissions.PolicyPayment.Default);
        DefinePermission(group, "PaymentDetails", WismaAppPermissions.PolicyPaymentDetails.Default);
        DefinePermission(group, "PaymentGuest", WismaAppPermissions.PolicyPaymentGuest.Default);
        DefinePermission(group, "FoodAndBeverageType", WismaAppPermissions.PolicyFoodAndBeverageType.Default);
        DefinePermission(group, "FoodAndBeverage", WismaAppPermissions.PolicyFoodAndBeverage.Default);
        DefinePermission(group, "ServiceType", WismaAppPermissions.PolicyServiceType.Default);
        DefinePermission(group, "Service", WismaAppPermissions.PolicyService.Default);
        DefinePermission(group, "MasterCompany", WismaAppPermissions.PolicyMasterCompany.Default);
        DefinePermission(group, "DiningOptions", WismaAppPermissions.PolicyDiningOptions.Default);
        DefinePermission(group, "MasterStatus", WismaAppPermissions.PolicyMasterStatus.Default);
        DefinePermission(group, "PaymentMethod", WismaAppPermissions.PolicyPaymentMethod.Default);
        DefinePermission(group, "Tax", WismaAppPermissions.PolicyTax.Default);
        DefinePermission(group, "Report", WismaAppPermissions.PolicyReport.Default);
        DefinePermission(group, "LostItem", WismaAppPermissions.PolicyLostItem.Default);

        group.AddPermission(WismaAppPermissions.PolicyReservation.Default + ".CheckIn", L("CheckIn"));
        group.AddPermission(WismaAppPermissions.PolicyReservation.Default + ".CheckOut", L("CheckOut"));
        group.AddPermission(WismaAppPermissions.PolicyReservation.Default + ".RoomService", L("RoomService"));
        group.AddPermission(WismaAppPermissions.PolicyReservation.Default + ".RoomFoodAndBeverage", L("RoomFoodAndBeverage"));
        group.AddPermission(WismaAppPermissions.PolicySettings.Default + ".Settings", L("Settings"));
    }

    private void DefinePermission(PermissionGroupDefinition group, string displayName, string defaultPermission)
    {
        var permission = group.AddPermission(defaultPermission, L(displayName));
        permission.AddChild(defaultPermission + ".View", L("View"));
        permission.AddChild(defaultPermission + ".Create", L("Create"));
        permission.AddChild(defaultPermission + ".Edit", L("Edit"));
        permission.AddChild(defaultPermission + ".Delete", L("Delete"));

        if (displayName == "Report")
        {
            permission.AddChild(defaultPermission + ".Export", L("Export"));
            permission.AddChild(defaultPermission + ".Execute", L("Execute"));
        }
    }

    private void DefinePermissionReadOnly(PermissionGroupDefinition group, string displayName, string defaultPermission)
    {
        var permission = group.AddPermission(defaultPermission, L(displayName));
        permission.AddChild(defaultPermission + ".View", L("View"));
        permission.AddChild(defaultPermission + ".Create", L("Create"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<IdentityServerResource>(name);
    }
}