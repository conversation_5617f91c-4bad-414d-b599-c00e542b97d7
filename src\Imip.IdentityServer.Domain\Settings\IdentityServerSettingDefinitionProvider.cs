using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Settings;

namespace Imip.IdentityServer.Settings;

public class IdentityServerSettingDefinitionProvider : SettingDefinitionProvider
{
    private readonly IConfiguration _configuration;

    public IdentityServerSettingDefinitionProvider(IServiceProvider serviceProvider)
    {
        _configuration = serviceProvider.GetRequiredService<IConfiguration>();
    }

    public override void Define(ISettingDefinitionContext context)
    {
        // Active Directory Settings
        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.Enabled,
                _configuration["ActiveDirectory:Enabled"] ?? "false",
                isVisibleToClients: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.Domain,
                _configuration["ActiveDirectory:Domain"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.LdapServer,
                _configuration["ActiveDirectory:LdapServer"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.BaseDn,
                _configuration["ActiveDirectory:BaseDn"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.Username,
                _configuration["ActiveDirectory:Username"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.Password,
                _configuration["ActiveDirectory:Password"] ?? "",
                isVisibleToClients: false,
                isEncrypted: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.Port,
                _configuration["ActiveDirectory:Port"] ?? "389",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.UseSsl,
                _configuration["ActiveDirectory:UseSsl"] ?? "false",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.AutoLogin,
                _configuration["ActiveDirectory:AutoLogin"] ?? "false",
                isVisibleToClients: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.TokenSecret,
                _configuration["ActiveDirectory:TokenSecret"] ?? Guid.NewGuid().ToString(),
                isVisibleToClients: false,
                isEncrypted: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.DefaultUsername,
                _configuration["ActiveDirectory:DefaultUsername"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.WindowsAuthEnabled,
                _configuration["ActiveDirectory:WindowsAuthEnabled"] ?? "false",
                isVisibleToClients: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.WindowsAuthServiceUrl,
                _configuration["ActiveDirectory:WindowsAuthServiceUrl"] ?? "",
                isVisibleToClients: false)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.WindowsAuthServiceApiKey,
                _configuration["ActiveDirectory:WindowsAuthServiceApiKey"] ?? "",
                isVisibleToClients: false,
                isEncrypted: true)
        );

        context.Add(
            new SettingDefinition(
                IdentityServerSettings.ActiveDirectory.WindowsAuthServiceTimeout,
                _configuration["ActiveDirectory:WindowsAuthServiceTimeout"] ?? "30",
                isVisibleToClients: false)
        );
    }
}
