using System;
using Volo.Abp.EventBus;

namespace Imip.IdentityServer.Events;

/// <summary>
/// Distributed event for notifying specific clients about logout
/// </summary>
[EventName("Imip.IdentityServer.ClientLogoutNotification")]
public class ClientLogoutNotificationEvent
{
    /// <summary>
    /// User ID who logged out
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Username of the user
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Session ID that was terminated
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// Tenant ID (for multi-tenant scenarios)
    /// </summary>
    public Guid? TenantId { get; set; }

    /// <summary>
    /// Target client application ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Client application name
    /// </summary>
    public string? ClientName { get; set; }

    /// <summary>
    /// Source application that initiated the logout
    /// </summary>
    public string SourceApplication { get; set; } = string.Empty;

    /// <summary>
    /// Type of logout notification
    /// </summary>
    public LogoutNotificationType NotificationType { get; set; }

    /// <summary>
    /// Logout URL for front-channel logout
    /// </summary>
    public string? LogoutUrl { get; set; }

    /// <summary>
    /// Back-channel logout URL for server-to-server notification
    /// </summary>
    public string? BackChannelLogoutUrl { get; set; }

    /// <summary>
    /// Timestamp when the logout occurred
    /// </summary>
    public DateTime LogoutTimestamp { get; set; }

    /// <summary>
    /// JWT token for back-channel logout (if applicable)
    /// </summary>
    public string? LogoutToken { get; set; }

    /// <summary>
    /// Additional metadata for the notification
    /// </summary>
    public string? Metadata { get; set; }

    public ClientLogoutNotificationEvent()
    {
        LogoutTimestamp = DateTime.UtcNow;
    }

    public ClientLogoutNotificationEvent(
        Guid userId,
        string userName,
        string clientId,
        string sourceApplication,
        LogoutNotificationType notificationType,
        string? sessionId = null,
        Guid? tenantId = null,
        string? clientName = null,
        string? logoutUrl = null,
        string? backChannelLogoutUrl = null,
        string? logoutToken = null)
    {
        UserId = userId;
        UserName = userName;
        ClientId = clientId;
        SourceApplication = sourceApplication;
        NotificationType = notificationType;
        SessionId = sessionId;
        TenantId = tenantId;
        ClientName = clientName;
        LogoutUrl = logoutUrl;
        BackChannelLogoutUrl = backChannelLogoutUrl;
        LogoutToken = logoutToken;
        LogoutTimestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Types of logout notifications
/// </summary>
public enum LogoutNotificationType
{
    /// <summary>
    /// Front-channel logout notification (browser redirect)
    /// </summary>
    FrontChannel = 0,

    /// <summary>
    /// Back-channel logout notification (server-to-server)
    /// </summary>
    BackChannel = 1,

    /// <summary>
    /// Both front and back channel notifications
    /// </summary>
    Both = 2
}
