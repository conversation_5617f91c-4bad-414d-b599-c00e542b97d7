{"ConnectionStrings": {"Default": "Server=localhost;Database=IdentityServer;User ID=sa;Password=*********;Integrated Security=false;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;"}, "Seq": {"ServerUrl": "http://localhost:5341", "ApiKey": "39EhmAhXiVEjnZulr1j0"}, "RabbitMQ": {"Connections": {"Default": {"HostName": "**********", "UserName": "guest", "Password": "guest", "Port": 5672}}, "EventBus": {"ClientName": "IdentityServer", "ExchangeName": "LogoutEvents"}}, "ActiveDirectory": {"Domain": "corp.imip.co.id", "LdapServer": "IMADDC01.corp.imip.co.id", "BaseDn": "DC=corp,DC=imip,DC=co,DC=id", "Username": "<EMAIL>", "Password": "Test*010203", "Port": "636", "UseSsl": "true", "Enabled": true, "AutoLogin": true, "TokenSecret": "your-secure-token-secret-key-change-this-in-production", "DefaultUsername": null, "WindowsAuthEnabled": true}, "OpenIddict": {"Applications": {"IdentityServerClient": {"ClientId": "IdentityServerClient", "ClientIdDev": "IdentityServerClientDev", "ClientIdProd": "IdentityServerClientProd", "RootUrl": "http://localhost:3000/", "RootUrlDev": "https://identity-dev.imip.co.id/", "RootUrlProd": "https://identity.imip.co.id/"}, "IdentityServerClientLocal": {"ClientId": "IdentityServerClientLocal", "RootUrl": "http://localhost:3000/"}, "IdentityServerClientLocalDev": {"ClientId": "IdentityServerClientLocalDev", "RootUrl": "http://localhost:3000/"}, "IdentityServerClientLocalDev2": {"ClientId": "IdentityServerClientLocalDev2", "RootUrl": "http://localhost:3000/"}, "IdentityServer_Swagger": {"ClientId": "IdentityServer_Swagger", "RootUrl": "https://localhost:44379/"}}}}