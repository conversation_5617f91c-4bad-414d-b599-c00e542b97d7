-- Script to fix corrupted encrypted settings in ABP Framework
-- This script removes corrupted encrypted settings so they can be re-seeded properly

-- Use this script when you encounter CryptographicException during setting decryption
-- The settings will be re-created with proper encryption during the next migration

USE [identityprovider_prd]; -- Change to your production database name
GO

-- Check current encrypted settings
SELECT 
    Name,
    Value,
    LEN(Value) as ValueLength,
    CASE 
        WHEN Name LIKE '%Password%' OR Name LIKE '%Secret%' OR Name LIKE '%Token%' THEN '[ENCRYPTED]'
        ELSE LEFT(Value, 50) + CASE WHEN LEN(Value) > 50 THEN '...' ELSE '' END
    END as DisplayValue
FROM AbpSettings 
WHERE Name IN (
    'IdentityServer.ActiveDirectory.Password',
    'IdentityServer.ActiveDirectory.TokenSecret',
    'IdentityServer.ActiveDirectory.WindowsAuthServiceApiKey'
)
ORDER BY Name;

-- Backup the current settings before deletion (optional)
-- Uncomment the following lines if you want to backup first:
/*
SELECT 
    Name,
    Value,
    ProviderName,
    ProviderKey,
    CreationTime,
    CreatorId,
    LastModificationTime,
    LastModifierId
INTO AbpSettings_Backup_EncryptedSettings
FROM AbpSettings 
WHERE Name IN (
    'IdentityServer.ActiveDirectory.Password',
    'IdentityServer.ActiveDirectory.TokenSecret',
    'IdentityServer.ActiveDirectory.WindowsAuthServiceApiKey'
);
*/

-- Remove corrupted encrypted settings
-- These will be re-created during the next migration with proper encryption
DELETE FROM AbpSettings 
WHERE Name IN (
    'IdentityServer.ActiveDirectory.TokenSecret'
);

-- Optionally, you can also remove other encrypted settings if they're causing issues:
-- Uncomment the following lines if needed:
/*
DELETE FROM AbpSettings 
WHERE Name IN (
    'IdentityServer.ActiveDirectory.Password',
    'IdentityServer.ActiveDirectory.WindowsAuthServiceApiKey'
);
*/

-- Verify deletion
SELECT 
    Name,
    Value,
    CASE 
        WHEN Name LIKE '%Password%' OR Name LIKE '%Secret%' OR Name LIKE '%Token%' THEN '[ENCRYPTED]'
        ELSE LEFT(Value, 50) + CASE WHEN LEN(Value) > 50 THEN '...' ELSE '' END
    END as DisplayValue
FROM AbpSettings 
WHERE Name IN (
    'IdentityServer.ActiveDirectory.Password',
    'IdentityServer.ActiveDirectory.TokenSecret',
    'IdentityServer.ActiveDirectory.WindowsAuthServiceApiKey'
)
ORDER BY Name;

PRINT 'Encrypted settings cleanup completed. Run the database migration again to re-seed the settings with proper encryption.';
GO
