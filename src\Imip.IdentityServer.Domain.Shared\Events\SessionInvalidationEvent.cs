using System;
using Volo.Abp.EventBus;

namespace Imip.IdentityServer.Events;

/// <summary>
/// Distributed event for session invalidation across all applications
/// </summary>
[EventName("Imip.IdentityServer.SessionInvalidation")]
public class SessionInvalidationEvent
{
    /// <summary>
    /// User ID whose sessions should be invalidated
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Specific session ID to invalidate (if null, invalidate all user sessions)
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// Tenant ID (for multi-tenant scenarios)
    /// </summary>
    public Guid? TenantId { get; set; }

    /// <summary>
    /// Source application that initiated the invalidation
    /// </summary>
    public string SourceApplication { get; set; } = string.Empty;

    /// <summary>
    /// Reason for session invalidation
    /// </summary>
    public SessionInvalidationReason Reason { get; set; }

    /// <summary>
    /// Timestamp when the invalidation was initiated
    /// </summary>
    public DateTime InvalidationTimestamp { get; set; }

    /// <summary>
    /// Whether to force logout from all devices
    /// </summary>
    public bool ForceLogoutAllDevices { get; set; }

    /// <summary>
    /// List of specific client applications to target (if null, target all)
    /// </summary>
    public string[]? TargetClients { get; set; }

    public SessionInvalidationEvent()
    {
        InvalidationTimestamp = DateTime.UtcNow;
    }

    public SessionInvalidationEvent(
        Guid userId,
        string sourceApplication,
        SessionInvalidationReason reason,
        string? sessionId = null,
        Guid? tenantId = null,
        bool forceLogoutAllDevices = false,
        string[]? targetClients = null)
    {
        UserId = userId;
        SourceApplication = sourceApplication;
        Reason = reason;
        SessionId = sessionId;
        TenantId = tenantId;
        ForceLogoutAllDevices = forceLogoutAllDevices;
        TargetClients = targetClients;
        InvalidationTimestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Reasons for session invalidation
/// </summary>
public enum SessionInvalidationReason
{
    /// <summary>
    /// User initiated logout
    /// </summary>
    UserLogout = 0,

    /// <summary>
    /// Session expired
    /// </summary>
    SessionExpired = 1,

    /// <summary>
    /// Security policy violation
    /// </summary>
    SecurityViolation = 2,

    /// <summary>
    /// Administrative action
    /// </summary>
    Administrative = 3,

    /// <summary>
    /// Password changed
    /// </summary>
    PasswordChanged = 4,

    /// <summary>
    /// Account locked
    /// </summary>
    AccountLocked = 5
}
