using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Users;
using Imip.IdentityServer.Events;
using Asp.Versioning;
using System.Linq;

namespace Imip.IdentityServer.Web.Controllers.Api;

/// <summary>
/// Custom Account controller that extends ABP's account functionality with logout event publishing
/// </summary>
[RemoteService(Name = "Account")]
[Area("account")]
[ControllerName("Account")]
[Route("api/account")]
public class AccountController : AbpControllerBase
{
    private readonly ILogger<AccountController> _logger;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly SignInManager<Volo.Abp.Identity.IdentityUser> _signInManager;
    private readonly IdentityUserManager _userManager;
    private readonly ICurrentUser _currentUser;

    public AccountController(
        ILogger<AccountController> logger,
        IDistributedEventBus distributedEventBus,
        SignInManager<Volo.Abp.Identity.IdentityUser> signInManager,
        IdentityUserManager userManager,
        ICurrentUser currentUser)
    {
        _logger = logger;
        _distributedEventBus = distributedEventBus;
        _signInManager = signInManager;
        _userManager = userManager;
        _currentUser = currentUser;
    }

    /// <summary>
    /// Logout endpoint that publishes distributed logout events
    /// </summary>
    [HttpGet("logout")]
    [HttpPost("logout")]
    [Authorize]
    public async Task<IActionResult> LogoutAsync()
    {
        try
        {
            if (!_currentUser.IsAuthenticated)
            {
                _logger.LogWarning("Logout attempt by unauthenticated user");
                return Ok(new { success = true, message = "User was not authenticated" });
            }

            var userId = _currentUser.Id!.Value;
            var userName = _currentUser.UserName ?? "Unknown";
            var sessionId = _currentUser.FindClaim(Volo.Abp.Security.Claims.AbpClaimTypes.SessionId)?.Value;
            var tenantId = _currentUser.TenantId;

            _logger.LogInformation(
                "Processing logout for user {UserId} ({UserName}), session: {SessionId}",
                userId, userName, sessionId);

            // Get additional context information
            var ipAddress = GetClientIpAddress();
            var userAgent = Request.Headers["User-Agent"].ToString();

            // Publish the logout event before signing out
            var logoutEvent = new UserLogoutEvent(
                userId: userId,
                userName: userName,
                sourceApplication: "IdentityServer",
                logoutType: LogoutType.Manual,
                sessionId: sessionId,
                tenantId: tenantId,
                ipAddress: ipAddress,
                userAgent: userAgent
            );

            await _distributedEventBus.PublishAsync(logoutEvent);

            _logger.LogDebug(
                "Published logout event for user {UserId}",
                userId);

            // Perform the actual logout
            await _signInManager.SignOutAsync();

            // Clear any additional cookies if needed
            ClearAuthenticationCookies();

            _logger.LogInformation(
                "Successfully logged out user {UserId} ({UserName})",
                userId, userName);

            return Ok(new
            {
                success = true,
                message = "Logout successful",
                timestamp = DateTime.UtcNow,
                userId = userId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout process");

            // Still try to sign out even if event publishing fails
            try
            {
                await _signInManager.SignOutAsync();
            }
            catch (Exception signOutEx)
            {
                _logger.LogError(signOutEx, "Error during sign out after logout event failure");
            }

            return StatusCode(500, new
            {
                success = false,
                message = "An error occurred during logout",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Administrative logout endpoint for logging out specific users
    /// </summary>
    [HttpPost("logout/{userId:guid}")]
    [Authorize] // Add appropriate authorization policy for admin users
    public async Task<IActionResult> LogoutUserAsync(Guid userId)
    {
        try
        {
            var targetUser = await _userManager.FindByIdAsync(userId.ToString());
            if (targetUser == null)
            {
                return NotFound(new { success = false, message = "User not found" });
            }

            _logger.LogInformation(
                "Processing administrative logout for user {UserId} ({UserName}) by {AdminUserId}",
                userId, targetUser.UserName, _currentUser.Id);

            // Publish administrative logout event
            var logoutEvent = new UserLogoutEvent(
                userId: userId,
                userName: targetUser.UserName!,
                sourceApplication: "IdentityServer",
                logoutType: LogoutType.Administrative,
                tenantId: targetUser.TenantId,
                ipAddress: GetClientIpAddress(),
                userAgent: Request.Headers["User-Agent"].ToString()
            );

            await _distributedEventBus.PublishAsync(logoutEvent);

            _logger.LogInformation(
                "Successfully published administrative logout event for user {UserId}",
                userId);

            return Ok(new
            {
                success = true,
                message = "Administrative logout initiated",
                targetUserId = userId,
                targetUserName = targetUser.UserName,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error during administrative logout for user {UserId}",
                userId);

            return StatusCode(500, new
            {
                success = false,
                message = "An error occurred during administrative logout",
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Logout all sessions for the current user
    /// </summary>
    [HttpPost("logout-all-sessions")]
    [Authorize]
    public async Task<IActionResult> LogoutAllSessionsAsync()
    {
        try
        {
            if (!_currentUser.IsAuthenticated)
            {
                return Unauthorized();
            }

            var userId = _currentUser.Id!.Value;
            var userName = _currentUser.UserName ?? "Unknown";
            var tenantId = _currentUser.TenantId;

            _logger.LogInformation(
                "Processing logout all sessions for user {UserId} ({UserName})",
                userId, userName);

            // Publish logout event for all sessions
            var logoutEvent = new UserLogoutEvent(
                userId: userId,
                userName: userName,
                sourceApplication: "IdentityServer",
                logoutType: LogoutType.Manual,
                sessionId: null, // null means all sessions
                tenantId: tenantId,
                ipAddress: GetClientIpAddress(),
                userAgent: Request.Headers["User-Agent"].ToString()
            );

            await _distributedEventBus.PublishAsync(logoutEvent);

            // Sign out the current session
            await _signInManager.SignOutAsync();
            ClearAuthenticationCookies();

            _logger.LogInformation(
                "Successfully logged out all sessions for user {UserId}",
                userId);

            return Ok(new
            {
                success = true,
                message = "All sessions logged out successfully",
                userId = userId,
                timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout all sessions");

            return StatusCode(500, new
            {
                success = false,
                message = "An error occurred during logout all sessions",
                error = ex.Message
            });
        }
    }

    private string? GetClientIpAddress()
    {
        try
        {
            // Try to get the real IP address from various headers
            var ipAddress = Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(ipAddress))
            {
                // X-Forwarded-For can contain multiple IPs, take the first one
                ipAddress = ipAddress.Split(',')[0].Trim();
            }

            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = Request.Headers["X-Real-IP"].FirstOrDefault();
            }

            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
            }

            return ipAddress;
        }
        catch
        {
            return null;
        }
    }

    private void ClearAuthenticationCookies()
    {
        try
        {
            // Clear any additional authentication-related cookies
            var cookiesToClear = new[]
            {
                "IMIP_SSO_TOKEN", // Custom SSO token from the existing implementation
                ".AspNetCore.Identity.Application",
                ".AspNetCore.Antiforgery",
                "__RequestVerificationToken"
            };

            foreach (var cookieName in cookiesToClear)
            {
                if (Request.Cookies.ContainsKey(cookieName))
                {
                    Response.Cookies.Delete(cookieName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error clearing authentication cookies");
        }
    }
}
