﻿apiVersion: v1
kind: Secret
metadata:
  name: imip-identity-secrets
  namespace: imip-identity-dev
type: Opaque
stringData:
  ConnectionStrings__Default: "${DEV_DB_CONNECTION}"
  AuthServer__CertificatePassPhrase: "${CERT_PASSPHRASE}"
  StringEncryption__DefaultPassPhrase: "${ENCRYPTION_PASSPHRASE}"
  Seq__ApiKey: "${SEQ_API_KEY}"
  ActiveDirectory__Password: "${AD_DEV_PASSWORD}"
  ActiveDirectory__TokenSecret: "${AD_DEV_TOKEN_SECRET}"
