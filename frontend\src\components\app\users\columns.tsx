'use client'

import { type IdentityUserDto, type IdentityUserUpdateDto } from '@/client'
import { type ColumnDef } from '@tanstack/react-table'
import { UserActions } from './UserActions'
import { StatusBadge } from './StatusBadge'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/data-table/DataTableColumnHeader'

// Type for the callback function to handle user actions
type UserActionCallback = (userId: string, userDto: IdentityUserUpdateDto, dialogType: 'edit' | 'permission' | 'delete') => void

// Function to create user columns with the action callback
export const getUserColumns = (
  handleUserAction: UserActionCallback
): ColumnDef<IdentityUserDto>[] => {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected()
              ? true
              : table.getIsSomeRowsSelected()
                ? "indeterminate"
                : false
          }
          onCheckedChange={() => table.toggleAllPageRowsSelected()}
          className="translate-y-0.5"
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={() => row.toggleSelected()}
          className="translate-y-0.5"
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
      meta: {
        displayName: "Select",
      },
    },
    {
      accessorKey: 'userName',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Username" />,
      enableHiding: true,
      enableSorting: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Username",
      },
    },
    {
      accessorKey: 'name',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
      enableHiding: true,
      enableSorting: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Name",
      },
    },
    {
      accessorKey: 'extraProperties.Company',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Company" />,
      enableHiding: true,
      enableSorting: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Company",
      },
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
      enableSorting: true,
      enableHiding: true,
      cell: (info) => info.getValue(),
      meta: {
        className: "text-left",
        displayName: "Email",
      },
    },
    {
      accessorKey: 'isActive',
      header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
      enableHiding: true,
      enableSorting: true,
      cell: (info) => <StatusBadge status={info.getValue() as boolean} />,
      meta: {
        className: "text-left",
        displayName: "Status",
      },
    },
    {
      id: 'actions',
      header: "Actions",
      enableHiding: true,
      cell: (info) => (
        <UserActions
          userId={info.row.original.id!}
          userDto={info.row.original as IdentityUserUpdateDto}
          onAction={handleUserAction}
          variant="dropdown" // Use "dropdown" for the first image style or "buttons" for the second image style
        />
      ),
      meta: {
        className: "text-right",
        displayName: "Actions",
      },
    },
  ]
}

