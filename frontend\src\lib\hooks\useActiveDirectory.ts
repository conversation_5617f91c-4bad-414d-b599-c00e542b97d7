import { type ActiveDirectorySettingsDto, getApiActiveDirectorySettings } from '@/client'
import { type UseQueryResult, useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

/**
 * Custom hook to fetch email settings using React Query.
 *
 * This hook uses the `useQuery` hook from React Query to fetch email settings data.
 * It returns a `UseQueryResult` containing the email settings data or an error.
 *
 * @returns {UseQueryResult<ActiveDirectorySettingsDto, unknown>} The result of the email settings query.
 */
export const useActiveDirectory = (): UseQueryResult<ActiveDirectorySettingsDto, unknown> => {
  return useQuery({
    queryKey: [QueryNames.GetActiveDirectory],
    queryFn: async () => {
      const { data } = await getApiActiveDirectorySettings()
      return data!
    },
  })
}
