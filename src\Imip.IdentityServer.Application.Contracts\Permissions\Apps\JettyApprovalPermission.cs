using Imip.IdentityServer.Permissions.Base;

namespace Imip.IdentityServer.Permissions.Apps;

public class JettyApprovalPermission
{
    public const string GroupName = "JettyApprovalApp";

    public static class PolicyJettyRequest
    {
        public const string Default = GroupName + ".JettyRequest";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyJettyRequestItem
    {
        public const string Default = GroupName + ".JettyRequestItem";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyAttachment
    {
        public const string Default = GroupName + ".Attachment";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyReport
    {
        public const string Default = GroupName + ".Report";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyJettySchedule
    {
        public const string Default = GroupName + ".JettySchedule";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyJettyDockedVessel
    {
        public const string Default = GroupName + ".JettyDockedVessel";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyJettyManage
    {
        public const string Default = GroupName + ".JettyManage";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyDocumentTemplate
    {
        public const string Default = GroupName + ".DocumentTemplate";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyApprovalTemplate
    {
        public const string Default = GroupName + ".ApprovalTemplate";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyApprovalRequest
    {
        public const string Default = GroupName + ".ApprovalRequest";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyApprovalApprover
    {
        public const string Default = GroupName + ".ApprovalApprover";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }

    public static class PolicyApprovalCriteria
    {
        public const string Default = GroupName + ".ApprovalCriteria";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyApprovalStages
    {
        public const string Default = GroupName + ".ApprovalStages";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
    public static class PolicyApprovalDelegation
    {
        public const string Default = GroupName + ".ApprovalDelegation";
        public const string View = Default + BasePermissions.StandardOperations.View;
        public const string Create = Default + BasePermissions.StandardOperations.Create;
        public const string Edit = Default + BasePermissions.StandardOperations.Edit;
        public const string Delete = Default + BasePermissions.StandardOperations.Delete;
    }
}
