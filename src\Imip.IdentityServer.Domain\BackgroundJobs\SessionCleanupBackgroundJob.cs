using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Uow;
using Imip.IdentityServer.Domain.Services;

namespace Imip.IdentityServer.Domain.BackgroundJobs;

/// <summary>
/// Background job for cleaning up expired sessions
/// </summary>
public class SessionCleanupBackgroundJob : AsyncBackgroundJob<SessionCleanupArgs>, ITransientDependency
{
    private readonly ILogger<SessionCleanupBackgroundJob> _logger;
    private readonly SessionManagementService _sessionManagementService;

    public SessionCleanupBackgroundJob(
        ILogger<SessionCleanupBackgroundJob> logger,
        SessionManagementService sessionManagementService)
    {
        _logger = logger;
        _sessionManagementService = sessionManagementService;
    }

    [UnitOfWork]
    public override async Task ExecuteAsync(SessionCleanupArgs args)
    {
        _logger.LogInformation("Starting session cleanup job with max age: {MaxAge}", args.MaxAge);

        try
        {
            await _sessionManagementService.CleanupExpiredSessionsAsync(args.MaxAge);
            
            _logger.LogInformation("Session cleanup job completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during session cleanup job execution");
            throw;
        }
    }
}

/// <summary>
/// Arguments for session cleanup background job
/// </summary>
public class SessionCleanupArgs
{
    /// <summary>
    /// Maximum age of sessions to keep
    /// </summary>
    public TimeSpan MaxAge { get; set; } = TimeSpan.FromDays(30);

    public SessionCleanupArgs()
    {
    }

    public SessionCleanupArgs(TimeSpan maxAge)
    {
        MaxAge = maxAge;
    }
}
