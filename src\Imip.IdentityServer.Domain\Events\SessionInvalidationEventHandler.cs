using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Events;

/// <summary>
/// Handles SessionInvalidationEvent to invalidate sessions across the system
/// </summary>
public class SessionInvalidationEventHandler : IDistributedEventHandler<SessionInvalidationEvent>, ITransientDependency
{
    private readonly ILogger<SessionInvalidationEventHandler> _logger;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public SessionInvalidationEventHandler(
        ILogger<SessionInvalidationEventHandler> logger,
        IRepository<IdentitySession, Guid> sessionRepository,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _logger = logger;
        _sessionRepository = sessionRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public async Task HandleEventAsync(SessionInvalidationEvent eventData)
    {
        _logger.LogInformation(
            "Processing session invalidation for user {UserId} from {SourceApplication}, reason: {Reason}",
            eventData.UserId, eventData.SourceApplication, eventData.Reason);

        try
        {
            using var uow = _unitOfWorkManager.Begin();

            await InvalidateSessionsAsync(eventData);

            await uow.CompleteAsync();

            _logger.LogInformation(
                "Successfully processed session invalidation for user {UserId}",
                eventData.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing session invalidation for user {UserId} from {SourceApplication}",
                eventData.UserId, eventData.SourceApplication);
            throw;
        }
    }

    private async Task InvalidateSessionsAsync(SessionInvalidationEvent eventData)
    {
        try
        {
            var queryable = await _sessionRepository.GetQueryableAsync();

            // Build the query based on the event data
            var query = queryable.Where(s => s.UserId == eventData.UserId);

            // Filter by tenant if specified
            if (eventData.TenantId.HasValue)
            {
                query = query.Where(s => s.TenantId == eventData.TenantId.Value);
            }

            // Filter by specific session if specified
            if (!string.IsNullOrEmpty(eventData.SessionId))
            {
                query = query.Where(s => s.SessionId == eventData.SessionId);
            }

            // Filter by target clients if specified
            if (eventData.TargetClients?.Any() == true)
            {
                query = query.Where(s => eventData.TargetClients.Contains(s.ClientId));
            }

            var sessions = query.ToList();

            if (sessions.Any())
            {
                _logger.LogInformation(
                    "Invalidating {SessionCount} sessions for user {UserId}, reason: {Reason}",
                    sessions.Count, eventData.UserId, eventData.Reason);

                foreach (var session in sessions)
                {
                    if (eventData.ForceLogoutAllDevices || ShouldInvalidateSession(session, eventData))
                    {
                        // Mark session as invalidated by setting SignedIn to a past date
                        var invalidatedSession = new IdentitySession(
                            session.Id,
                            session.SessionId,
                            session.Device,
                            session.DeviceInfo,
                            session.UserId,
                            session.TenantId,
                            session.ClientId,
                            null, // IP Address
                            DateTime.UtcNow.AddYears(-1), // Mark as logged out
                            session.LastAccessed
                        );

                        await _sessionRepository.UpdateAsync(invalidatedSession);

                        _logger.LogDebug(
                            "Invalidated session {SessionId} for user {UserId}",
                            session.SessionId, eventData.UserId);
                    }
                }
            }
            else
            {
                _logger.LogDebug(
                    "No sessions found to invalidate for user {UserId}",
                    eventData.UserId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error invalidating sessions for user {UserId}",
                eventData.UserId);
            throw;
        }
    }

    private static bool ShouldInvalidateSession(IdentitySession session, SessionInvalidationEvent eventData)
    {
        // Determine if a specific session should be invalidated based on the reason
        return eventData.Reason switch
        {
            SessionInvalidationReason.UserLogout => true,
            SessionInvalidationReason.SessionExpired => true,
            SessionInvalidationReason.SecurityViolation => true,
            SessionInvalidationReason.Administrative => true,
            SessionInvalidationReason.PasswordChanged => true,
            SessionInvalidationReason.AccountLocked => true,
            _ => true
        };
    }
}
