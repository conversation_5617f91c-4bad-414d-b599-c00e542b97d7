using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.OpenIddict.Applications;
using Volo.Abp.Uow;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Events;

/// <summary>
/// Handles UserLogoutEvent to propagate logout across all connected applications
/// </summary>
public class UserLogoutEventHandler : IDistributedEventHandler<UserLogoutEvent>, ITransientDependency
{
    private readonly ILogger<UserLogoutEventHandler> _logger;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;
    private readonly IRepository<OpenIddictApplication, Guid> _applicationRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public UserLogoutEventHandler(
        ILogger<UserLogoutEventHandler> logger,
        IDistributedEventBus distributedEventBus,
        IRepository<IdentitySession, Guid> sessionRepository,
        IRepository<OpenIddictApplication, Guid> applicationRepository,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _logger = logger;
        _distributedEventBus = distributedEventBus;
        _sessionRepository = sessionRepository;
        _applicationRepository = applicationRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }

    public async Task HandleEventAsync(UserLogoutEvent eventData)
    {
        _logger.LogInformation(
            "Processing logout event for user {UserId} ({UserName}) from {SourceApplication}",
            eventData.UserId, eventData.UserName, eventData.SourceApplication);

        try
        {
            using var uow = _unitOfWorkManager.Begin();

            // 1. Invalidate user sessions
            await InvalidateUserSessionsAsync(eventData);

            // 2. Notify all connected client applications
            await NotifyClientApplicationsAsync(eventData);

            // 3. Publish session invalidation event
            await PublishSessionInvalidationEventAsync(eventData);

            await uow.CompleteAsync();

            _logger.LogInformation(
                "Successfully processed logout event for user {UserId}",
                eventData.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error processing logout event for user {UserId} from {SourceApplication}",
                eventData.UserId, eventData.SourceApplication);
            throw;
        }
    }

    private async Task InvalidateUserSessionsAsync(UserLogoutEvent eventData)
    {
        try
        {
            var sessions = await _sessionRepository.GetListAsync(
                s => s.UserId == eventData.UserId &&
                     (eventData.SessionId == null || s.SessionId == eventData.SessionId) &&
                     (eventData.TenantId == null || s.TenantId == eventData.TenantId));

            if (sessions.Any())
            {
                _logger.LogInformation(
                    "Invalidating {SessionCount} sessions for user {UserId}",
                    sessions.Count, eventData.UserId);

                // Mark sessions as signed out by updating the SignedIn timestamp
                foreach (var session in sessions)
                {
                    // Create a new session with updated SignedIn time to mark as logged out
                    var updatedSession = new IdentitySession(
                        session.Id,
                        session.SessionId,
                        session.Device,
                        session.DeviceInfo,
                        session.UserId,
                        session.TenantId,
                        session.ClientId,
                        null, // IP Address
                        DateTime.UtcNow.AddYears(-1), // Set to past date to indicate logout
                        session.LastAccessed
                    );

                    await _sessionRepository.UpdateAsync(updatedSession);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error invalidating sessions for user {UserId}",
                eventData.UserId);
            throw;
        }
    }

    private async Task NotifyClientApplicationsAsync(UserLogoutEvent eventData)
    {
        try
        {
            // Get all client applications that should be notified
            var applications = await GetTargetApplicationsAsync(eventData);

            foreach (var application in applications)
            {
                // Skip the source application to avoid circular notifications
                if (string.Equals(application.ClientId, eventData.SourceApplication, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                await PublishClientLogoutNotificationAsync(eventData, application);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error notifying client applications for user {UserId}",
                eventData.UserId);
            throw;
        }
    }

    private async Task<List<OpenIddictApplication>> GetTargetApplicationsAsync(UserLogoutEvent eventData)
    {
        if (eventData.TargetClients?.Any() == true)
        {
            // Get specific target clients
            return await _applicationRepository.GetListAsync(
                a => eventData.TargetClients.Contains(a.ClientId!));
        }

        // Get all applications with logout URLs configured
        var allApplications = await _applicationRepository.GetListAsync();
        return allApplications.Where(a =>
            !string.IsNullOrEmpty(a.PostLogoutRedirectUris) ||
            !string.IsNullOrEmpty(a.Properties)).ToList();
    }

    private async Task PublishClientLogoutNotificationAsync(UserLogoutEvent eventData, OpenIddictApplication application)
    {
        try
        {
            var notificationEvent = new ClientLogoutNotificationEvent(
                eventData.UserId,
                eventData.UserName,
                application.ClientId!,
                eventData.SourceApplication,
                LogoutNotificationType.Both, // Support both front and back channel
                eventData.SessionId,
                eventData.TenantId,
                application.DisplayName,
                GetFrontChannelLogoutUrl(application),
                GetBackChannelLogoutUrl(application)
            );

            await _distributedEventBus.PublishAsync(notificationEvent);

            _logger.LogDebug(
                "Published logout notification for client {ClientId} for user {UserId}",
                application.ClientId, eventData.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error publishing logout notification for client {ClientId} for user {UserId}",
                application.ClientId, eventData.UserId);
        }
    }

    private async Task PublishSessionInvalidationEventAsync(UserLogoutEvent eventData)
    {
        try
        {
            var invalidationEvent = new SessionInvalidationEvent(
                eventData.UserId,
                eventData.SourceApplication,
                SessionInvalidationReason.UserLogout,
                eventData.SessionId,
                eventData.TenantId,
                eventData.LogoutType == LogoutType.Administrative,
                eventData.TargetClients
            );

            await _distributedEventBus.PublishAsync(invalidationEvent);

            _logger.LogDebug(
                "Published session invalidation event for user {UserId}",
                eventData.UserId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Error publishing session invalidation event for user {UserId}",
                eventData.UserId);
        }
    }

    private static string? GetFrontChannelLogoutUrl(OpenIddictApplication application)
    {
        // Extract front-channel logout URL from application properties or redirect URIs
        // This is a simplified implementation - you may need to adjust based on your OpenIddict configuration
        return application.PostLogoutRedirectUris?.Split(' ', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault();
    }

    private static string? GetBackChannelLogoutUrl(OpenIddictApplication application)
    {
        // Extract back-channel logout URL from application properties
        // This would typically be stored in the application's Properties field
        // You may need to implement custom logic based on how you store this information
        return null; // Implement based on your application configuration
    }
}
