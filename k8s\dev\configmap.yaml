﻿apiVersion: v1
kind: ConfigMap
metadata:
  name: imip-identity-config
  namespace: imip-identity-dev
data:
  ASPNETCORE_ENVIRONMENT: "Development"
  App__SelfUrl: "${DEV_APP_URL}"
  App__ClientUrl: "${DEV_CLIENT_URL}"
  App__CorsOrigins: "${DEV_CORS_ORIGINS}"
  App__AppName: "${DEV_APP_NAME}"
  App__HealthCheckUrl: "/api/health/kubernetes"
  AuthServer__Authority: "${DEV_APP_URL}"
  AuthServer__RequireHttpsMetadata: "false"
  AuthServer__CertificatePath: "/app/certs/identity-server.pfx"
  Seq__ServerUrl: "${SEQ_SERVER_URL}"
  ExternalAuth__ApiUrl: "${DEV_EXTERNAL_AUTH_URL}"
  ExternalAuth__Enabled: "true"
  Redis__IsEnabled: "true"
  ActiveDirectory__Domain: "corp.imip.co.id"
  ActiveDirectory__LdapServer: "imaddc01.corp.imip.co.id"
  ActiveDirectory__BaseDn: "DC=corp,DC=imip,DC=co,DC=id"
  ActiveDirectory__Username: "<EMAIL>"
  ActiveDirectory__Port: "636"
  ActiveDirectory__UseSsl: "true"
  ActiveDirectory__Enabled: "true"
  ActiveDirectory__AutoLogin: "true"
  ActiveDirectory__DefaultUsername: ""
  ActiveDirectory__WindowsAuthEnabled: "true"
  ActiveDirectory__WindowsAuthServiceUrl: "https://your-windows-auth-service.com"
  ActiveDirectory__WindowsAuthServiceApiKey: ""
  ActiveDirectory__WindowsAuthServiceTimeout: "30"
  Redis__Configuration: "${DEV_REDIS_CONFIGURATION},abortConnect=false,connectTimeout=30000,syncTimeout=30000,connectRetry=10,keepAlive=60,allowAdmin=true,responseTimeout=30000"
  ConnectionStrings__Default__CommandTimeout: "60"
  ConnectionStrings__Default__ConnectTimeout: "60"
  ConnectionStrings__Default__ConnectRetryCount: "5"
  ConnectionStrings__Default__ConnectRetryInterval: "10"
  RabbitMQ__Connections__Default__HostName: "${RABBITMQ_HOST}"
  RabbitMQ__Connections__Default__UserName: "${RABBITMQ_USERNAME}"
  RabbitMQ__Connections__Default__Password: "${RABBITMQ_PASSWORD}"
  RabbitMQ__Connections__Default__Port: "${RABBITMQ_PORT}"
  RabbitMQ__EventBus__ClientName: "IdentityServer-Dev"
  RabbitMQ__EventBus__ExchangeName: "${RABBITMQ_EXCHANGE_NAME}"
