import { getApiOpeniddictApplicationsPermissions, getApiOpeniddictRequirements } from '@/client'
import { useQuery } from '@tanstack/react-query'
import { QueryNames } from './QueryConstants'

export const useOpeniddictApplicationsPermissions = () => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictApplicationsPermissions],
    queryFn: async () => {
      const response = await getApiOpeniddictApplicationsPermissions()
      return response.data?.data
    },
  })
}

export const useOpeniddictRequirements = () => {
  return useQuery({
    queryKey: [QueryNames.GetOpeniddictRequirements],
    queryFn: async () => {
      const response = await getApiOpeniddictRequirements()
      return response.data?.data
    },
  })
}
