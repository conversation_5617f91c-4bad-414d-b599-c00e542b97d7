using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using Imip.IdentityServer.Events;

namespace Imip.IdentityServer.Domain.Services;

/// <summary>
/// Service for managing user sessions and handling logout propagation
/// </summary>
public class SessionManagementService : DomainService, ITransientDependency
{
    private readonly ILogger<SessionManagementService> _logger;
    private readonly IRepository<IdentitySession, Guid> _sessionRepository;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IUnitOfWorkManager _unitOfWorkManager;

    public SessionManagementService(
        ILogger<SessionManagementService> logger,
        IRepository<IdentitySession, Guid> sessionRepository,
        IDistributedEventBus distributedEventBus,
        IUnitOfWorkManager unitOfWorkManager)
    {
        _logger = logger;
        _sessionRepository = sessionRepository;
        _distributedEventBus = distributedEventBus;
        _unitOfWorkManager = unitOfWorkManager;
    }

    /// <summary>
    /// Get all active sessions for a user
    /// </summary>
    public async Task<List<IdentitySession>> GetActiveUserSessionsAsync(Guid userId, Guid? tenantId = null)
    {
        var cutoffTime = DateTime.UtcNow.AddDays(-1); // Consider sessions from last 24 hours as potentially active

        var sessions = await _sessionRepository.GetListAsync(s =>
            s.UserId == userId &&
            (tenantId == null || s.TenantId == tenantId) &&
            s.SignedIn > cutoffTime);

        return sessions.Where(s => IsSessionActive(s)).ToList();
    }

    /// <summary>
    /// Get session information by session ID
    /// </summary>
    public async Task<IdentitySession?> GetSessionAsync(string sessionId)
    {
        return await _sessionRepository.FirstOrDefaultAsync(s => s.SessionId == sessionId);
    }

    /// <summary>
    /// Invalidate a specific session
    /// </summary>
    public async Task InvalidateSessionAsync(string sessionId, SessionInvalidationReason reason, string sourceApplication)
    {
        using var uow = _unitOfWorkManager.Begin();

        var session = await _sessionRepository.FirstOrDefaultAsync(s => s.SessionId == sessionId);
        if (session == null)
        {
            _logger.LogWarning("Session {SessionId} not found for invalidation", sessionId);
            return;
        }

        _logger.LogInformation(
            "Invalidating session {SessionId} for user {UserId}, reason: {Reason}",
            sessionId, session.UserId, reason);

        // Mark session as invalidated
        var invalidatedSession = new IdentitySession(
            session.Id,
            session.SessionId,
            session.Device,
            session.DeviceInfo,
            session.UserId,
            session.TenantId,
            session.ClientId,
            null, // IP Address
            DateTime.UtcNow.AddYears(-1), // Mark as logged out
            session.LastAccessed
        );

        await _sessionRepository.UpdateAsync(invalidatedSession);

        // Publish session invalidation event
        var invalidationEvent = new SessionInvalidationEvent(
            session.UserId,
            sourceApplication,
            reason,
            sessionId,
            session.TenantId
        );

        await _distributedEventBus.PublishAsync(invalidationEvent);

        await uow.CompleteAsync();
    }

    /// <summary>
    /// Invalidate all sessions for a user
    /// </summary>
    public async Task InvalidateAllUserSessionsAsync(Guid userId, SessionInvalidationReason reason, string sourceApplication, Guid? tenantId = null)
    {
        using var uow = _unitOfWorkManager.Begin();

        var activeSessions = await GetActiveUserSessionsAsync(userId, tenantId);

        if (activeSessions.Count == 0)
        {
            _logger.LogDebug("No active sessions found for user {UserId}", userId);
            return;
        }

        _logger.LogInformation(
            "Invalidating {SessionCount} sessions for user {UserId}, reason: {Reason}",
            activeSessions.Count, userId, reason);

        foreach (var session in activeSessions)
        {
            // Mark session as invalidated
            var invalidatedSession = new IdentitySession(
                session.Id,
                session.SessionId,
                session.Device,
                session.DeviceInfo,
                session.UserId,
                session.TenantId,
                session.ClientId,
                null, // IP Address
                DateTime.UtcNow.AddYears(-1), // Mark as logged out
                session.LastAccessed
            );

            await _sessionRepository.UpdateAsync(invalidatedSession);
        }

        // Publish session invalidation event for all sessions
        var invalidationEvent = new SessionInvalidationEvent(
            userId,
            sourceApplication,
            reason,
            null, // null sessionId means all sessions
            tenantId,
            true // force logout all devices
        );

        await _distributedEventBus.PublishAsync(invalidationEvent);

        await uow.CompleteAsync();
    }

    /// <summary>
    /// Update session last accessed time
    /// </summary>
    public async Task UpdateSessionLastAccessedAsync(string sessionId)
    {
        try
        {
            var session = await _sessionRepository.FirstOrDefaultAsync(s => s.SessionId == sessionId);
            if (session == null)
            {
                return;
            }

            // Only update if it's been more than 5 minutes since last update to avoid excessive database calls
            if (DateTime.UtcNow - session.LastAccessed < TimeSpan.FromMinutes(5))
            {
                return;
            }

            var updatedSession = new IdentitySession(
                session.Id,
                session.SessionId,
                session.Device,
                session.DeviceInfo,
                session.UserId,
                session.TenantId,
                session.ClientId,
                null, // IP Address
                session.SignedIn,
                DateTime.UtcNow // Update last accessed
            );

            await _sessionRepository.UpdateAsync(updatedSession);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating session last accessed time for session {SessionId}", sessionId);
        }
    }

    /// <summary>
    /// Check if a session is considered active
    /// </summary>
    public bool IsSessionActive(IdentitySession session)
    {
        // Session is active if:
        // 1. SignedIn time is not in the past (indicating logout)
        // 2. Last accessed is within reasonable time frame
        var isSignedIn = session.SignedIn > DateTime.UtcNow.AddDays(-1);
        var isRecentlyAccessed = session.LastAccessed > DateTime.UtcNow.AddHours(-24);

        return isSignedIn && isRecentlyAccessed;
    }

    /// <summary>
    /// Get session statistics for a user
    /// </summary>
    public async Task<SessionStatistics> GetUserSessionStatisticsAsync(Guid userId, Guid? tenantId = null)
    {
        var allSessions = await _sessionRepository.GetListAsync(s =>
            s.UserId == userId &&
            (tenantId == null || s.TenantId == tenantId));

        var activeSessions = allSessions.Where(s => IsSessionActive(s)).ToList();
        var recentSessions = allSessions.Where(s => s.LastAccessed > DateTime.UtcNow.AddDays(-7)).ToList();

        return new SessionStatistics
        {
            UserId = userId,
            TenantId = tenantId,
            TotalSessions = allSessions.Count,
            ActiveSessions = activeSessions.Count,
            RecentSessions = recentSessions.Count,
            LastActivity = (DateTime)(allSessions.Count > 0 ? allSessions.Max(s => s.LastAccessed) : DateTime.MinValue),
            DeviceTypes = activeSessions.GroupBy(s => s.Device).ToDictionary(g => g.Key, g => g.Count()),
            ClientApplications = activeSessions.GroupBy(s => s.ClientId).ToDictionary(g => g.Key, g => g.Count())
        };
    }

    /// <summary>
    /// Clean up expired sessions
    /// </summary>
    public async Task CleanupExpiredSessionsAsync(TimeSpan maxAge)
    {
        var cutoffTime = DateTime.UtcNow - maxAge;

        var expiredSessions = await _sessionRepository.GetListAsync(s =>
            s.LastAccessed < cutoffTime ||
            s.SignedIn < DateTime.UtcNow.AddDays(-30)); // Sessions marked as logged out more than 30 days ago

        if (expiredSessions.Count > 0)
        {
            _logger.LogInformation("Cleaning up {ExpiredSessionCount} expired sessions", expiredSessions.Count);

            foreach (var session in expiredSessions)
            {
                await _sessionRepository.DeleteAsync(session);
            }
        }
    }
}

/// <summary>
/// Session statistics for a user
/// </summary>
public class SessionStatistics
{
    public Guid UserId { get; set; }
    public Guid? TenantId { get; set; }
    public int TotalSessions { get; set; }
    public int ActiveSessions { get; set; }
    public int RecentSessions { get; set; }
    public DateTime LastActivity { get; set; }
    public Dictionary<string, int> DeviceTypes { get; set; } = new();
    public Dictionary<string, int> ClientApplications { get; set; } = new();
}
